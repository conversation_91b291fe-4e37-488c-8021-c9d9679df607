// AWS Summit India 2025 Session Data - Sample Data for Demo
export const sessionsData = [
  {
    "id": "1_z8wpg66k",
    "name": "AWS Summit Bengaluru: Innovators Edition Keynote",
    "description": "The convergence of generative AI and cloud computing is creating unprecedented opportunities for building innovative solutions. Join us for the keynote from AWS Summit Bengaluru: Innovators Edition and discover how AWS is uniquely positioned in the current AI revolution.",
    "type": "7",
    "updatedAt": 1748352434,
    "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/621922200/thumbnail/entry_id/1_z8wpg66k/version/100011/width/379/height/213/type/3/quality/100",
    "tags": "Level 100 - Introductory,Keynote",
    "mediaType": 201,
    "duration": 0,
    "schedulingData": {
      "start": {
        "timestamp": 1750912200,
        "timeZoneName": "Asia/Kolkata",
        "timeZoneOffset": 19800
      },
      "end": {
        "timestamp": 1750916700,
        "timeZoneName": "Asia/Kolkata",
        "timeZoneOffset": 19800
      }
    },
    "presenters": [
      {
        "id": "presenter1",
        "name": "<PERSON><PERSON>",
        "title": "President, India and South Asia, A<PERSON>",
        "bio": "",
        "link": "/profile/presenter1"
      },
      {
        "id": "presenter2",
        "name": "Sirish Chandrasekaran",
        "title": "Vice President, Analytics, AWS",
        "bio": "",
        "link": "/profile/presenter2"
      }
    ],
    "customData": {
      "SessionLevel": "Level 100 - Introductory",
      "Topics": [
        "Analytics",
        "Generative AI",
        "Artificial Intelligence",
        "Machine Learning",
        "Building Modern Applications",
        "Compute"
      ],
      "SessionType": "Keynote",
      "SessionID": "KEY001",
      "Tracks": "Keynote"
    },
    "stats": [],
    "hiddenTags": "analytics,generative ai,artificial intelligence,machine learning,building modern applications,compute",
    "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OnlineKEY001",
    "canAddToWatchList": true
  },
  {
    "id": "1_ass7x75l",
    "name": "Build without limits with AWS Cloud infrastructure",
    "description": "Organizations of all sizes are looking to solve big challenges, turn ideas into reality, and innovate faster. This requires a secure and reliable infrastructure that delivers high performance at low costs and scales without limits.",
    "type": "7",
    "updatedAt": 1748352516,
    "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/621922200/thumbnail/entry_id/1_ass7x75l/version/100011/width/379/height/213/type/3/quality/100",
    "tags": "Level 100 - Introductory,Building for scale",
    "mediaType": 201,
    "duration": 0,
    "schedulingData": {
      "start": {
        "timestamp": 1750917600,
        "timeZoneName": "Asia/Kolkata",
        "timeZoneOffset": 19800
      },
      "end": {
        "timestamp": 1750919400,
        "timeZoneName": "Asia/Kolkata",
        "timeZoneOffset": 19800
      }
    },
    "presenters": [
      {
        "id": "presenter3",
        "name": "Sathish Hariharan",
        "title": "Senior Solutions Architect, AWS India",
        "bio": "",
        "link": "/profile/presenter3"
      }
    ],
    "customData": {
      "SessionLevel": "Level 100 - Introductory",
      "Topics": [
        "Scale on AWS",
        "Compute"
      ],
      "SessionType": "Breakout Session",
      "SessionID": "OET401",
      "Tracks": "Building for scale"
    },
    "stats": [],
    "hiddenTags": "scale on aws,compute,breakout session",
    "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET401",
    "canAddToWatchList": true
  },
  {
    "id": "1_f4gu0cm8",
    "name": "Gen AI in action: from POC to business value",
    "description": "More global leaders like Bundesliga and Trellix are taking their generative AI applications to production and realizing business impact through increased innovation, productivity, and cost savings.",
    "type": "7",
    "updatedAt": 1748520618,
    "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/621922200/thumbnail/entry_id/1_f4gu0cm8/version/100011/width/379/height/213/type/3/quality/100",
    "tags": "Level 100 - Introductory,Generative AI on AWS",
    "mediaType": 201,
    "duration": 0,
    "schedulingData": {
      "start": {
        "timestamp": 1750917600,
        "timeZoneName": "Asia/Kolkata",
        "timeZoneOffset": 19800
      },
      "end": {
        "timestamp": 1750919400,
        "timeZoneName": "Asia/Kolkata",
        "timeZoneOffset": 19800
      }
    },
    "presenters": [
      {
        "id": "presenter4",
        "name": "Arun Nalpet Ramakrishna",
        "title": "Senior Solutions Architect, AWS India",
        "bio": "",
        "link": "/profile/presenter4"
      }
    ],
    "customData": {
      "SessionLevel": "Level 100 - Introductory",
      "Topics": [
        "Artificial Intelligence",
        "Generative AI",
        "Machine Learning"
      ],
      "SessionType": "Breakout Session",
      "SessionID": "OET101",
      "Tracks": "Generative AI on AWS"
    },
    "stats": [],
    "hiddenTags": "artificial intelligence,generative ai,machine learning,breakout session",
    "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET101",
    "canAddToWatchList": true
  }
];

export default sessionsData;
