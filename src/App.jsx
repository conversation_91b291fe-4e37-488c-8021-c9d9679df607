import { useState, useEffect, useMemo } from 'react';
import { Calendar, Clock, Users, Filter, Search, Download } from 'lucide-react';
import { sessionsData } from './data.js';

function App() {
  const [sessions, setSessions] = useState([]);
  const [selectedSessions, setSelectedSessions] = useState([]);

  // Load session data on mount
  useEffect(() => {
    setSessions(sessionsData);
  }, []);

  const handleSessionSelect = (sessionId) => {
    setSelectedSessions(prev => {
      if (prev.includes(sessionId)) {
        return prev.filter(id => id !== sessionId);
      } else {
        return [...prev, sessionId];
      }
    });
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp * 1000).toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Kolkata'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-aws-blue text-white shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">AWS Summit India 2025</h1>
              <p className="text-blue-200 mt-1">
                Interactive Session Dashboard • June 26, 2025
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm text-blue-200">Sessions Selected</div>
                <div className="text-2xl font-bold">{selectedSessions.length}</div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Stats Bar */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <Calendar className="h-5 w-5 text-gray-500" />
                <span className="text-sm text-gray-600">
                  {sessions.length} sessions available
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-gray-500" />
                <span className="text-sm text-gray-600">
                  Multiple tracks and levels
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sessions.map(session => (
            <div key={session.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 text-lg leading-tight mb-2">
                    {session.name}
                  </h3>

                  {session.schedulingData && (
                    <div className="flex items-center text-sm text-gray-600 mb-2">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>
                        {formatTime(session.schedulingData.start.timestamp)} - {formatTime(session.schedulingData.end.timestamp)}
                      </span>
                    </div>
                  )}
                </div>

                <button
                  onClick={() => handleSessionSelect(session.id)}
                  className={`px-3 py-2 rounded-lg font-medium text-sm transition-colors ${
                    selectedSessions.includes(session.id)
                      ? 'bg-aws-orange text-white hover:bg-orange-600'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {selectedSessions.includes(session.id) ? 'Added' : 'Add'}
                </button>
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-3">
                {session.customData?.Tracks && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {session.customData.Tracks}
                  </span>
                )}

                {session.customData?.SessionLevel && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {session.customData.SessionLevel.replace(' - ', ' ')}
                  </span>
                )}
              </div>

              {/* Speakers */}
              {session.presenters && session.presenters.length > 0 && (
                <div className="mb-3">
                  <div className="flex items-center text-sm text-gray-600 mb-1">
                    <Users className="h-4 w-4 mr-1" />
                    <span className="font-medium">Speakers</span>
                  </div>
                  <div className="space-y-1">
                    {session.presenters.map((presenter, index) => (
                      <div key={index} className="text-sm">
                        <span className="font-medium text-gray-900">{presenter.name}</span>
                        {presenter.title && (
                          <span className="text-gray-600 ml-1">• {presenter.title}</span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Description */}
              <div className="text-sm text-gray-600 line-clamp-3">
                {session.description}
              </div>
            </div>
          ))}
        </div>

        {sessions.length === 0 && (
          <div className="text-center py-12">
            <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Loading sessions...</h3>
            <p className="text-gray-500">Please wait while we load the session data</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
