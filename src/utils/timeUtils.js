import { format, fromUnixTime, isSameDay, isWithinInterval } from 'date-fns';

export const formatTime = (timestamp) => {
  return format(fromUnixTime(timestamp), 'HH:mm');
};

export const formatDateTime = (timestamp) => {
  return format(fromUnixTime(timestamp), 'MMM dd, yyyy HH:mm');
};

export const formatDuration = (startTimestamp, endTimestamp) => {
  const start = fromUnixTime(startTimestamp);
  const end = fromUnixTime(endTimestamp);
  const durationMs = end.getTime() - start.getTime();
  const minutes = Math.floor(durationMs / (1000 * 60));
  
  if (minutes < 60) {
    return `${minutes}m`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${remainingMinutes}m`;
};

export const getTimeSlots = (sessions) => {
  const slots = new Set();
  
  sessions.forEach(session => {
    if (session.schedulingData) {
      slots.add(session.schedulingData.start.timestamp);
    }
  });
  
  return Array.from(slots).sort((a, b) => a - b);
};

export const sessionsOverlap = (session1, session2) => {
  if (!session1.schedulingData || !session2.schedulingData) {
    return false;
  }
  
  const start1 = session1.schedulingData.start.timestamp;
  const end1 = session1.schedulingData.end.timestamp;
  const start2 = session2.schedulingData.start.timestamp;
  const end2 = session2.schedulingData.end.timestamp;
  
  return start1 < end2 && start2 < end1;
};

export const getConflictingSessions = (selectedSessions, allSessions) => {
  const conflicts = new Set();
  
  selectedSessions.forEach(selectedId => {
    const selectedSession = allSessions.find(s => s.id === selectedId);
    if (!selectedSession) return;
    
    allSessions.forEach(session => {
      if (session.id !== selectedId && sessionsOverlap(selectedSession, session)) {
        conflicts.add(session.id);
      }
    });
  });
  
  return Array.from(conflicts);
};

export const getEventDate = (sessions) => {
  if (sessions.length === 0) return null;
  
  const firstSession = sessions.find(s => s.schedulingData);
  if (!firstSession) return null;
  
  return fromUnixTime(firstSession.schedulingData.start.timestamp);
};
