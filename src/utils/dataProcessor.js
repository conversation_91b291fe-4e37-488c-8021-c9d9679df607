export const processSessionData = (rawData) => {
  return rawData.map(session => ({
    ...session,
    // Normalize topics to always be an array
    topics: Array.isArray(session.customData?.Topics) 
      ? session.customData.Topics 
      : session.customData?.Topics 
        ? [session.customData.Topics] 
        : [],
    // Extract level number for easier sorting
    levelNumber: extractLevelNumber(session.customData?.SessionLevel),
    // Create searchable text
    searchText: createSearchText(session),
    // Calculate duration in minutes
    durationMinutes: session.schedulingData 
      ? (session.schedulingData.end.timestamp - session.schedulingData.start.timestamp) / 60
      : 0
  }));
};

const extractLevelNumber = (levelString) => {
  if (!levelString) return 0;
  const match = levelString.match(/Level (\d+)/);
  return match ? parseInt(match[1]) : 0;
};

const createSearchText = (session) => {
  const parts = [
    session.name,
    session.description,
    session.customData?.SessionType,
    session.customData?.Tracks,
    ...(session.presenters?.map(p => p.name) || []),
    ...(session.presenters?.map(p => p.title) || []),
    ...(Array.isArray(session.customData?.Topics) ? session.customData.Topics : [])
  ];
  
  return parts.filter(Boolean).join(' ').toLowerCase();
};

export const getUniqueValues = (sessions, field) => {
  const values = new Set();
  
  sessions.forEach(session => {
    switch (field) {
      case 'tracks':
        if (session.customData?.Tracks) {
          values.add(session.customData.Tracks);
        }
        break;
      case 'levels':
        if (session.customData?.SessionLevel) {
          values.add(session.customData.SessionLevel);
        }
        break;
      case 'sessionTypes':
        if (session.customData?.SessionType) {
          values.add(session.customData.SessionType);
        }
        break;
      case 'topics':
        if (session.topics) {
          session.topics.forEach(topic => values.add(topic));
        }
        break;
      case 'speakers':
        if (session.presenters) {
          session.presenters.forEach(presenter => values.add(presenter.name));
        }
        break;
    }
  });
  
  return Array.from(values).sort();
};

export const filterSessions = (sessions, filters) => {
  return sessions.filter(session => {
    // Search filter
    if (filters.search && !session.searchText.includes(filters.search.toLowerCase())) {
      return false;
    }
    
    // Track filter
    if (filters.tracks.length > 0 && !filters.tracks.includes(session.customData?.Tracks)) {
      return false;
    }
    
    // Level filter
    if (filters.levels.length > 0 && !filters.levels.includes(session.customData?.SessionLevel)) {
      return false;
    }
    
    // Session type filter
    if (filters.sessionTypes.length > 0 && !filters.sessionTypes.includes(session.customData?.SessionType)) {
      return false;
    }
    
    // Topics filter
    if (filters.topics.length > 0) {
      const hasMatchingTopic = session.topics.some(topic => filters.topics.includes(topic));
      if (!hasMatchingTopic) return false;
    }
    
    // Speaker filter
    if (filters.speakers.length > 0) {
      const hasMatchingSpeaker = session.presenters?.some(presenter => 
        filters.speakers.includes(presenter.name)
      );
      if (!hasMatchingSpeaker) return false;
    }
    
    return true;
  });
};

export const groupSessionsByTimeSlot = (sessions) => {
  const groups = {};
  
  sessions.forEach(session => {
    if (session.schedulingData) {
      const startTime = session.schedulingData.start.timestamp;
      if (!groups[startTime]) {
        groups[startTime] = [];
      }
      groups[startTime].push(session);
    }
  });
  
  return groups;
};

export const exportSchedule = (selectedSessions, allSessions) => {
  const selected = allSessions.filter(session => selectedSessions.includes(session.id));
  
  const scheduleData = {
    eventName: "AWS Summit India 2025",
    exportDate: new Date().toISOString(),
    sessions: selected.map(session => ({
      id: session.id,
      name: session.name,
      description: session.description,
      startTime: session.schedulingData?.start.timestamp,
      endTime: session.schedulingData?.end.timestamp,
      track: session.customData?.Tracks,
      level: session.customData?.SessionLevel,
      speakers: session.presenters?.map(p => ({
        name: p.name,
        title: p.title
      })) || [],
      topics: session.topics
    }))
  };
  
  return scheduleData;
};
