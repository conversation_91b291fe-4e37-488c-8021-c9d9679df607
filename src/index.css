@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .session-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow duration-200;
  }
  
  .timeline-slot {
    @apply border-l-4 border-gray-200 pl-4 py-2;
  }
  
  .timeline-slot.selected {
    @apply border-l-aws-orange bg-orange-50;
  }
  
  .timeline-slot.conflict {
    @apply border-l-red-500 bg-red-50;
  }
  
  .filter-chip {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 cursor-pointer transition-colors;
  }
  
  .filter-chip.active {
    @apply bg-aws-orange text-white hover:bg-orange-600;
  }
  
  .btn-primary {
    @apply bg-aws-orange text-white px-4 py-2 rounded-lg font-medium hover:bg-orange-600 transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-800 px-4 py-2 rounded-lg font-medium hover:bg-gray-300 transition-colors duration-200;
  }
}
