import { useMemo } from 'react';
import { Clock, Users, Calendar } from 'lucide-react';
import { formatTime, getTimeSlots, getConflictingSessions } from '../utils/timeUtils';
import { groupSessionsByTimeSlot } from '../utils/dataProcessor';

const Timeline = ({ 
  sessions, 
  selectedSessions, 
  onSessionSelect, 
  selectedTimeSlot, 
  onTimeSlotSelect 
}) => {
  const timeSlots = useMemo(() => getTimeSlots(sessions), [sessions]);
  const sessionGroups = useMemo(() => groupSessionsByTimeSlot(sessions), [sessions]);
  const conflictingSessions = useMemo(() => 
    getConflictingSessions(selectedSessions, sessions), 
    [selectedSessions, sessions]
  );

  const getTrackColor = (track) => {
    const colors = {
      'Keynote': 'bg-purple-500',
      'Generative AI on AWS': 'bg-blue-500',
      'AWS for data': 'bg-green-500',
      'Building for scale': 'bg-orange-500',
      'Security compliance & resilience': 'bg-red-500',
      'Migration & modernization': 'bg-indigo-500',
      'Future of Developers': 'bg-pink-500'
    };
    return colors[track] || 'bg-gray-500';
  };

  const getSessionStatus = (session) => {
    if (selectedSessions.includes(session.id)) return 'selected';
    if (conflictingSessions.includes(session.id)) return 'conflict';
    return 'default';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center">
          <Calendar className="h-5 w-5 mr-2" />
          Session Timeline
        </h2>
        <p className="text-sm text-gray-600 mt-1">
          Click on time slots to filter sessions, or click on sessions to add them to your schedule
        </p>
      </div>

      <div className="p-6">
        <div className="space-y-6">
          {timeSlots.map(timestamp => {
            const sessionsInSlot = sessionGroups[timestamp] || [];
            const isSelectedSlot = selectedTimeSlot === timestamp;
            
            return (
              <div key={timestamp} className="relative">
                {/* Time Header */}
                <div 
                  className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                    isSelectedSlot 
                      ? 'bg-aws-orange text-white' 
                      : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                  onClick={() => onTimeSlotSelect(isSelectedSlot ? null : timestamp)}
                >
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <span className="font-medium">
                      {formatTime(timestamp)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4" />
                    <span className="text-sm">
                      {sessionsInSlot.length} session{sessionsInSlot.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                </div>

                {/* Sessions in this time slot */}
                <div className="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {sessionsInSlot.map(session => {
                    const status = getSessionStatus(session);
                    const trackColor = getTrackColor(session.customData?.Tracks);
                    
                    return (
                      <div
                        key={session.id}
                        className={`p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                          status === 'selected'
                            ? 'border-aws-orange bg-orange-50 shadow-md'
                            : status === 'conflict'
                            ? 'border-red-500 bg-red-50'
                            : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
                        }`}
                        onClick={() => onSessionSelect(session.id)}
                      >
                        {/* Track indicator */}
                        <div className={`w-full h-1 ${trackColor} rounded-full mb-2`} />
                        
                        {/* Session info */}
                        <div className="space-y-1">
                          <h4 className="font-medium text-sm text-gray-900 line-clamp-2">
                            {session.name}
                          </h4>
                          
                          <div className="flex items-center justify-between text-xs text-gray-600">
                            <span className="bg-gray-100 px-2 py-1 rounded">
                              {session.customData?.Tracks}
                            </span>
                            <span className="bg-gray-100 px-2 py-1 rounded">
                              {session.customData?.SessionLevel?.replace(' - ', ' ')}
                            </span>
                          </div>
                          
                          {session.presenters && session.presenters.length > 0 && (
                            <div className="text-xs text-gray-500 truncate">
                              {session.presenters.map(p => p.name).join(', ')}
                            </div>
                          )}
                          
                          {status === 'conflict' && (
                            <div className="text-xs text-red-600 font-medium">
                              ⚠️ Time conflict
                            </div>
                          )}
                          
                          {status === 'selected' && (
                            <div className="text-xs text-orange-600 font-medium">
                              ✓ Added to schedule
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>

        {timeSlots.length === 0 && (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No sessions available</p>
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="flex flex-wrap items-center gap-4 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-50 border-2 border-aws-orange rounded" />
            <span>Selected</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-50 border-2 border-red-500 rounded" />
            <span>Conflict</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-white border-2 border-gray-200 rounded" />
            <span>Available</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Timeline;
