import { useMemo } from 'react';
import { Calendar, Clock, X, AlertTriangle, Download, Users } from 'lucide-react';
import { formatTime, getConflictingSessions, sessionsOverlap } from '../utils/timeUtils';
import { exportSchedule } from '../utils/dataProcessor';

const ScheduleBuilder = ({ selectedSessions, allSessions, onSessionRemove }) => {
  const selectedSessionData = useMemo(() => {
    return allSessions.filter(session => selectedSessions.includes(session.id));
  }, [selectedSessions, allSessions]);

  const conflicts = useMemo(() => {
    const conflictPairs = [];
    
    for (let i = 0; i < selectedSessionData.length; i++) {
      for (let j = i + 1; j < selectedSessionData.length; j++) {
        if (sessionsOverlap(selectedSessionData[i], selectedSessionData[j])) {
          conflictPairs.push([selectedSessionData[i], selectedSessionData[j]]);
        }
      }
    }
    
    return conflictPairs;
  }, [selectedSessionData]);

  const sortedSessions = useMemo(() => {
    return [...selectedSessionData].sort((a, b) => {
      if (!a.schedulingData || !b.schedulingData) return 0;
      return a.schedulingData.start.timestamp - b.schedulingData.start.timestamp;
    });
  }, [selectedSessionData]);

  const totalDuration = useMemo(() => {
    return selectedSessionData.reduce((total, session) => {
      if (session.schedulingData) {
        const duration = (session.schedulingData.end.timestamp - session.schedulingData.start.timestamp) / 60;
        return total + duration;
      }
      return total;
    }, 0);
  }, [selectedSessionData]);

  const handleExportSchedule = () => {
    const scheduleData = exportSchedule(selectedSessions, allSessions);
    const blob = new Blob([JSON.stringify(scheduleData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'aws-summit-india-2025-my-schedule.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getTrackColor = (track) => {
    const colors = {
      'Keynote': 'bg-purple-100 text-purple-800',
      'Generative AI on AWS': 'bg-blue-100 text-blue-800',
      'AWS for data': 'bg-green-100 text-green-800',
      'Building for scale': 'bg-orange-100 text-orange-800',
      'Security compliance & resilience': 'bg-red-100 text-red-800',
      'Migration & modernization': 'bg-indigo-100 text-indigo-800',
      'Future of Developers': 'bg-pink-100 text-pink-800'
    };
    return colors[track] || 'bg-gray-100 text-gray-800';
  };

  if (selectedSessions.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 text-center">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Your Schedule</h3>
          <p className="text-gray-500 text-sm">
            Select sessions from the timeline or session cards to build your personalized schedule.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-gray-900 flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            My Schedule
          </h3>
          {selectedSessions.length > 0 && (
            <button
              onClick={handleExportSchedule}
              className="flex items-center space-x-1 text-sm text-aws-orange hover:text-orange-600"
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
          )}
        </div>
        
        {/* Stats */}
        <div className="mt-2 flex items-center space-x-4 text-sm text-gray-600">
          <span>{selectedSessions.length} sessions</span>
          <span>•</span>
          <span>{Math.round(totalDuration / 60 * 10) / 10}h total</span>
        </div>
      </div>

      {/* Conflicts Warning */}
      {conflicts.length > 0 && (
        <div className="p-4 bg-red-50 border-b border-red-200">
          <div className="flex items-center text-sm text-red-700 mb-2">
            <AlertTriangle className="h-4 w-4 mr-2" />
            <span className="font-medium">
              {conflicts.length} time conflict{conflicts.length !== 1 ? 's' : ''} detected
            </span>
          </div>
          <div className="space-y-1">
            {conflicts.map((conflictPair, index) => (
              <div key={index} className="text-xs text-red-600">
                "{conflictPair[0].name}" overlaps with "{conflictPair[1].name}"
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Session List */}
      <div className="divide-y divide-gray-200">
        {sortedSessions.map(session => {
          const hasConflict = conflicts.some(pair => 
            pair[0].id === session.id || pair[1].id === session.id
          );

          return (
            <div key={session.id} className={`p-4 ${hasConflict ? 'bg-red-50' : ''}`}>
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  {/* Time */}
                  {session.schedulingData && (
                    <div className="flex items-center text-sm text-gray-600 mb-1">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>
                        {formatTime(session.schedulingData.start.timestamp)} - {formatTime(session.schedulingData.end.timestamp)}
                      </span>
                      {hasConflict && (
                        <AlertTriangle className="h-4 w-4 ml-2 text-red-500" />
                      )}
                    </div>
                  )}
                  
                  {/* Session Name */}
                  <h4 className="font-medium text-gray-900 text-sm leading-tight mb-2">
                    {session.name}
                  </h4>
                  
                  {/* Track and Level */}
                  <div className="flex items-center space-x-2 mb-2">
                    {session.customData?.Tracks && (
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTrackColor(session.customData.Tracks)}`}>
                        {session.customData.Tracks}
                      </span>
                    )}
                    {session.customData?.SessionLevel && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {session.customData.SessionLevel.replace(' - ', ' ')}
                      </span>
                    )}
                  </div>
                  
                  {/* Speakers */}
                  {session.presenters && session.presenters.length > 0 && (
                    <div className="flex items-center text-xs text-gray-600">
                      <Users className="h-3 w-3 mr-1" />
                      <span className="truncate">
                        {session.presenters.map(p => p.name).join(', ')}
                      </span>
                    </div>
                  )}
                </div>
                
                {/* Remove Button */}
                <button
                  onClick={() => onSessionRemove(session.id)}
                  className="ml-2 p-1 text-gray-400 hover:text-red-500 transition-colors"
                  aria-label="Remove from schedule"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className="p-4 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            Total: {selectedSessions.length} sessions
          </span>
          <span className="text-gray-600">
            Duration: {Math.round(totalDuration / 60 * 10) / 10} hours
          </span>
        </div>
        
        {conflicts.length > 0 && (
          <div className="mt-2 text-xs text-red-600">
            ⚠️ Please resolve time conflicts before finalizing your schedule
          </div>
        )}
      </div>
    </div>
  );
};

export default ScheduleBuilder;
