import { useState, useRef, useEffect } from 'react';
import { Search, X } from 'lucide-react';

const SearchBar = ({ value, onChange, placeholder = "Search sessions, speakers, topics..." }) => {
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef(null);

  const handleClear = () => {
    onChange('');
    inputRef.current?.focus();
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      inputRef.current?.blur();
    }
  };

  // Focus on Ctrl/Cmd + K
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        inputRef.current?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className={`h-5 w-5 ${isFocused ? 'text-aws-orange' : 'text-gray-400'}`} />
          </div>
          
          <input
            ref={inputRef}
            type="text"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className={`block w-full pl-10 pr-10 py-3 border rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:border-transparent transition-colors ${
              isFocused 
                ? 'border-aws-orange focus:ring-aws-orange' 
                : 'border-gray-300 focus:ring-gray-500'
            }`}
          />
          
          {value && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <button
                onClick={handleClear}
                className="text-gray-400 hover:text-gray-600 focus:outline-none"
                aria-label="Clear search"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>
        
        {/* Search hints */}
        <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-4">
            <span>Search by session name, speaker, or topic</span>
          </div>
          <div className="hidden sm:flex items-center space-x-1">
            <kbd className="px-2 py-1 bg-gray-100 border border-gray-300 rounded text-xs">
              {navigator.platform.includes('Mac') ? '⌘' : 'Ctrl'}
            </kbd>
            <kbd className="px-2 py-1 bg-gray-100 border border-gray-300 rounded text-xs">K</kbd>
            <span>to focus</span>
          </div>
        </div>
        
        {/* Search suggestions/results count */}
        {value && (
          <div className="mt-2 text-sm text-gray-600">
            <span>Searching for: </span>
            <span className="font-medium text-gray-900">"{value}"</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchBar;
