import { useState } from 'react';
import { ChevronDown, ChevronUp, X } from 'lucide-react';
import { getUniqueValues } from '../utils/dataProcessor';

const FilterPanel = ({ sessions, filters, onChange }) => {
  const [expandedSections, setExpandedSections] = useState({
    tracks: true,
    levels: true,
    sessionTypes: false,
    topics: false,
    speakers: false
  });

  const filterOptions = {
    tracks: getUniqueValues(sessions, 'tracks'),
    levels: getUniqueValues(sessions, 'levels'),
    sessionTypes: getUniqueValues(sessions, 'sessionTypes'),
    topics: getUniqueValues(sessions, 'topics'),
    speakers: getUniqueValues(sessions, 'speakers')
  };

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleFilterToggle = (filterType, value) => {
    const currentValues = filters[filterType];
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];
    
    onChange({
      ...filters,
      [filterType]: newValues
    });
  };

  const clearAllFilters = () => {
    onChange({
      search: '',
      tracks: [],
      levels: [],
      sessionTypes: [],
      topics: [],
      speakers: []
    });
  };

  const getActiveFilterCount = () => {
    return Object.entries(filters).reduce((count, [key, value]) => {
      if (key === 'search') return count + (value ? 1 : 0);
      return count + (Array.isArray(value) ? value.length : 0);
    }, 0);
  };

  const FilterSection = ({ title, filterKey, options, maxVisible = 5 }) => {
    const isExpanded = expandedSections[filterKey];
    const activeFilters = filters[filterKey];
    const visibleOptions = isExpanded ? options : options.slice(0, maxVisible);
    const hasMore = options.length > maxVisible;

    return (
      <div className="border-b border-gray-200 pb-4">
        <button
          onClick={() => toggleSection(filterKey)}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="font-medium text-gray-900">
            {title}
            {activeFilters.length > 0 && (
              <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-aws-orange text-white">
                {activeFilters.length}
              </span>
            )}
          </h3>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          )}
        </button>

        {isExpanded && (
          <div className="mt-3 space-y-2">
            {visibleOptions.map(option => (
              <label key={option} className="flex items-center">
                <input
                  type="checkbox"
                  checked={activeFilters.includes(option)}
                  onChange={() => handleFilterToggle(filterKey, option)}
                  className="h-4 w-4 text-aws-orange focus:ring-aws-orange border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700 flex-1">
                  {option}
                </span>
                <span className="text-xs text-gray-500">
                  {sessions.filter(session => {
                    switch (filterKey) {
                      case 'tracks':
                        return session.customData?.Tracks === option;
                      case 'levels':
                        return session.customData?.SessionLevel === option;
                      case 'sessionTypes':
                        return session.customData?.SessionType === option;
                      case 'topics':
                        return session.topics?.includes(option);
                      case 'speakers':
                        return session.presenters?.some(p => p.name === option);
                      default:
                        return false;
                    }
                  }).length}
                </span>
              </label>
            ))}
            
            {hasMore && !isExpanded && (
              <button
                onClick={() => toggleSection(filterKey)}
                className="text-sm text-aws-orange hover:text-orange-600 font-medium"
              >
                Show {options.length - maxVisible} more...
              </button>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="font-semibold text-gray-900">Filters</h2>
          {getActiveFilterCount() > 0 && (
            <button
              onClick={clearAllFilters}
              className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-800"
            >
              <X className="h-4 w-4" />
              <span>Clear all</span>
            </button>
          )}
        </div>
        {getActiveFilterCount() > 0 && (
          <p className="text-sm text-gray-600 mt-1">
            {getActiveFilterCount()} filter{getActiveFilterCount() !== 1 ? 's' : ''} active
          </p>
        )}
      </div>

      <div className="p-4 space-y-4">
        <FilterSection
          title="Tracks"
          filterKey="tracks"
          options={filterOptions.tracks}
        />

        <FilterSection
          title="Session Levels"
          filterKey="levels"
          options={filterOptions.levels}
        />

        <FilterSection
          title="Session Types"
          filterKey="sessionTypes"
          options={filterOptions.sessionTypes}
        />

        <FilterSection
          title="Topics"
          filterKey="topics"
          options={filterOptions.topics}
          maxVisible={8}
        />

        <FilterSection
          title="Speakers"
          filterKey="speakers"
          options={filterOptions.speakers}
          maxVisible={6}
        />
      </div>

      {/* Active Filters Summary */}
      {getActiveFilterCount() > 0 && (
        <div className="p-4 bg-gray-50 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Active Filters:</h4>
          <div className="flex flex-wrap gap-2">
            {filters.search && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Search: "{filters.search}"
                <button
                  onClick={() => onChange({ ...filters, search: '' })}
                  className="ml-1 hover:text-blue-600"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            )}
            
            {Object.entries(filters).map(([key, values]) => {
              if (key === 'search' || !Array.isArray(values) || values.length === 0) return null;
              
              return values.map(value => (
                <span
                  key={`${key}-${value}`}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-aws-orange text-white"
                >
                  {value}
                  <button
                    onClick={() => handleFilterToggle(key, value)}
                    className="ml-1 hover:text-orange-200"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              ));
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterPanel;
