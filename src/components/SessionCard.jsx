import { useState } from 'react';
import { Clock, Users, Tag, ChevronDown, ChevronUp, Plus, Check, AlertTriangle } from 'lucide-react';
import { formatTime, formatDuration } from '../utils/timeUtils';

const SessionCard = ({ session, isSelected, onSelect, conflictingSessions = [] }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const hasConflict = conflictingSessions.includes(session.id);

  const getTrackColor = (track) => {
    const colors = {
      'Keynote': 'bg-purple-100 text-purple-800 border-purple-200',
      'Generative AI on AWS': 'bg-blue-100 text-blue-800 border-blue-200',
      'AWS for data': 'bg-green-100 text-green-800 border-green-200',
      'Building for scale': 'bg-orange-100 text-orange-800 border-orange-200',
      'Security compliance & resilience': 'bg-red-100 text-red-800 border-red-200',
      'Migration & modernization': 'bg-indigo-100 text-indigo-800 border-indigo-200',
      'Future of Developers': 'bg-pink-100 text-pink-800 border-pink-200'
    };
    return colors[track] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getLevelColor = (level) => {
    if (level?.includes('100')) return 'bg-green-100 text-green-800';
    if (level?.includes('200')) return 'bg-yellow-100 text-yellow-800';
    if (level?.includes('300')) return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className={`session-card ${isSelected ? 'ring-2 ring-aws-orange' : ''} ${hasConflict ? 'ring-2 ring-red-500' : ''}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900 text-lg leading-tight mb-2">
            {session.name}
          </h3>
          
          {/* Time and Duration */}
          {session.schedulingData && (
            <div className="flex items-center text-sm text-gray-600 mb-2">
              <Clock className="h-4 w-4 mr-1" />
              <span>
                {formatTime(session.schedulingData.start.timestamp)} - {formatTime(session.schedulingData.end.timestamp)}
              </span>
              <span className="mx-2">•</span>
              <span>
                {formatDuration(session.schedulingData.start.timestamp, session.schedulingData.end.timestamp)}
              </span>
            </div>
          )}
        </div>

        {/* Select Button */}
        <button
          onClick={onSelect}
          className={`flex items-center space-x-1 px-3 py-2 rounded-lg font-medium text-sm transition-colors ${
            isSelected
              ? 'bg-aws-orange text-white hover:bg-orange-600'
              : hasConflict
              ? 'bg-red-100 text-red-700 hover:bg-red-200'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          {isSelected ? (
            <>
              <Check className="h-4 w-4" />
              <span>Added</span>
            </>
          ) : hasConflict ? (
            <>
              <AlertTriangle className="h-4 w-4" />
              <span>Conflict</span>
            </>
          ) : (
            <>
              <Plus className="h-4 w-4" />
              <span>Add</span>
            </>
          )}
        </button>
      </div>

      {/* Tags */}
      <div className="flex flex-wrap gap-2 mb-3">
        {session.customData?.Tracks && (
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getTrackColor(session.customData.Tracks)}`}>
            <Tag className="h-3 w-3 mr-1" />
            {session.customData.Tracks}
          </span>
        )}
        
        {session.customData?.SessionLevel && (
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(session.customData.SessionLevel)}`}>
            {session.customData.SessionLevel.replace(' - ', ' ')}
          </span>
        )}
        
        {session.customData?.SessionType && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {session.customData.SessionType}
          </span>
        )}
      </div>

      {/* Speakers */}
      {session.presenters && session.presenters.length > 0 && (
        <div className="mb-3">
          <div className="flex items-center text-sm text-gray-600 mb-1">
            <Users className="h-4 w-4 mr-1" />
            <span className="font-medium">Speakers</span>
          </div>
          <div className="space-y-1">
            {session.presenters.map((presenter, index) => (
              <div key={index} className="text-sm">
                <span className="font-medium text-gray-900">{presenter.name}</span>
                {presenter.title && (
                  <span className="text-gray-600 ml-1">• {presenter.title}</span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Topics */}
      {session.topics && session.topics.length > 0 && (
        <div className="mb-3">
          <div className="flex flex-wrap gap-1">
            {session.topics.slice(0, 3).map((topic, index) => (
              <span key={index} className="inline-block px-2 py-1 bg-gray-50 text-gray-700 text-xs rounded">
                {topic}
              </span>
            ))}
            {session.topics.length > 3 && (
              <span className="inline-block px-2 py-1 bg-gray-50 text-gray-700 text-xs rounded">
                +{session.topics.length - 3} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Description (expandable) */}
      <div className="mb-3">
        <div className={`text-sm text-gray-600 ${isExpanded ? '' : 'line-clamp-3'}`}>
          {session.description}
        </div>
        
        {session.description && session.description.length > 200 && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center text-sm text-aws-orange hover:text-orange-600 mt-1 font-medium"
          >
            {isExpanded ? (
              <>
                <ChevronUp className="h-4 w-4 mr-1" />
                Show less
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-1" />
                Show more
              </>
            )}
          </button>
        )}
      </div>

      {/* Session ID */}
      {session.customData?.SessionID && (
        <div className="text-xs text-gray-500 font-mono">
          ID: {session.customData.SessionID}
        </div>
      )}

      {/* Conflict Warning */}
      {hasConflict && (
        <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center text-sm text-red-700">
            <AlertTriangle className="h-4 w-4 mr-2" />
            <span className="font-medium">Time Conflict</span>
          </div>
          <p className="text-xs text-red-600 mt-1">
            This session overlaps with your selected sessions
          </p>
        </div>
      )}
    </div>
  );
};

export default SessionCard;
