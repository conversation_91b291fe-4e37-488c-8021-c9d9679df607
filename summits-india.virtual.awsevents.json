[{"id": "1_z8wpg66k", "name": "AWS\n  Summit Bengaluru: Innovators Edition Keynote", "description": "The  convergence of generative AI and cloud computing is creating unprecedented  opportunities for building innovative solutions. Join us for the keynote from AWS Summit Bengaluru: Innovators Edition and discover how AWS is uniquely  positioned in the current AI revolution, tracing the technological evolution  that has brought us to this transformative era. You will be guided through  the entire customer adoption journey, from robust infrastructure to  cutting-edge applications, with a focus on business-critical use cases and  the crucial role of data as a key differentiator. Learn from real-world  success stories, featuring innovative approaches of building generative AI  solutions on AWS, and strategies for developing secure, modern applications  on the cloud. Whether you're interested in AI-driven transformation or  looking to scale your cloud infrastructure, this keynote will provide you  with practical insights and inspiration. Don't miss this opportunity to be at  the forefront of the AI and cloud revolution.", "type": "7", "updatedAt": 1748352434, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_z8wpg66k/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Keynote", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": 1750916700, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6InZmWGN1NXlDOU83N1wvYjg3Q2NiQXR3PT0iLCJ2YWx1ZSI6IlkwbU9Kd2pWYlV4QThmTkJNYnpCNDBkQmpXZ3M5TWZWbGNOK2p3STZiTG89IiwibWFjIjoiNTg3OGQ0OWI1YTRjYzlhMGYxOGE2YjcyOWI2ZmE1MGU1NDAyMDA2OWY3OWJlMDY5ZmU0NWZlZjEwZWVjYWEzMiJ9", "name": "<PERSON><PERSON>", "title": "President, India and South Asia, AWS", "bio": "", "link": "/profile/eyJpdiI6InZmWGN1NXlDOU83N1wvYjg3Q2NiQXR3PT0iLCJ2YWx1ZSI6IlkwbU9Kd2pWYlV4QThmTkJNYnpCNDBkQmpXZ3M5TWZWbGNOK2p3STZiTG89IiwibWFjIjoiNTg3OGQ0OWI1YTRjYzlhMGYxOGE2YjcyOWI2ZmE1MGU1NDAyMDA2OWY3OWJlMDY5ZmU0NWZlZjEwZWVjYWEzMiJ9"}, {"id": "eyJpdiI6Ill0NWN4ZDEreXM0VXZtV1JubnJ3M0E9PSIsInZhbHVlIjoiZTZvXC9lRzdBS2JRUkZTNUFrc3hmN1Fja0ZpMmxYTU95M2Y1Z21jM0hWN2Yzd3Zxald6bGw3R1IzTm50MzVNeHAiLCJtYWMiOiJkNDNiMWMxYTFlM2I5YjFjZGNlOTdmNTNiYWJlMWJhNTg0MWVlYTc3Y2JmYTFmNzcyMDk1MWI0MzNiMzc1NzY4In0_", "name": "<PERSON><PERSON>", "title": "Vice President, Analytics, AWS", "bio": "", "link": "/profile/eyJpdiI6Ill0NWN4ZDEreXM0VXZtV1JubnJ3M0E9PSIsInZhbHVlIjoiZTZvXC9lRzdBS2JRUkZTNUFrc3hmN1Fja0ZpMmxYTU95M2Y1Z21jM0hWN2Yzd3Zxald6bGw3R1IzTm50MzVNeHAiLCJtYWMiOiJkNDNiMWMxYTFlM2I5YjFjZGNlOTdmNTNiYWJlMWJhNTg0MWVlYTc3Y2JmYTFmNzcyMDk1MWI0MzNiMzc1NzY4In0_"}, {"id": "eyJpdiI6InZVTFluY2VzZitzOUtIV040eG9CV3c9PSIsInZhbHVlIjoiV3Rnamd3WmQ4RlwvSGE2aWxtK25zUFV2V3FPdUFWaTFBXC9LWDlORDRYMXVBPSIsIm1hYyI6IjcyNmUyYjYxN2IzYWVmNzZlNjdlZjdlYzM0NzNhYmRkOWE4ZDliZmNmNDkzYzczZTc4YjQwMWY3ZjcxMzQ1MGQifQ__", "name": "<PERSON><PERSON>", "title": "EVP & Chief Technology Officer, Infosys", "bio": "", "link": "/profile/eyJpdiI6InZVTFluY2VzZitzOUtIV040eG9CV3c9PSIsInZhbHVlIjoiV3Rnamd3WmQ4RlwvSGE2aWxtK25zUFV2V3FPdUFWaTFBXC9LWDlORDRYMXVBPSIsIm1hYyI6IjcyNmUyYjYxN2IzYWVmNzZlNjdlZjdlYzM0NzNhYmRkOWE4ZDliZmNmNDkzYzczZTc4YjQwMWY3ZjcxMzQ1MGQifQ__"}, {"id": "eyJpdiI6IkdpamIycWh3b1dZNjNQZUd0OTJ6d3c9PSIsInZhbHVlIjoiYTBmT20rc3NRbTVPbU5uVGhXYjJqaHRTdnpjZG1qaktUVTZ2eG1PeWJibz0iLCJtYWMiOiI3NjYyYjc1NWEyMzg5ZDlkNDJiMGVjODA2ODMyZmYwZDYxZDUyMWRmMGVkMGQ4MGM1ZmM5OGZkYTQ2YWQzMmQwIn0_", "name": "<PERSON><PERSON>", "title": "Co-Founder, <PERSON><PERSON>", "bio": "", "link": "/profile/eyJpdiI6IkdpamIycWh3b1dZNjNQZUd0OTJ6d3c9PSIsInZhbHVlIjoiYTBmT20rc3NRbTVPbU5uVGhXYjJqaHRTdnpjZG1qaktUVTZ2eG1PeWJibz0iLCJtYWMiOiI3NjYyYjc1NWEyMzg5ZDlkNDJiMGVjODA2ODMyZmYwZDYxZDUyMWRmMGVkMGQ4MGM1ZmM5OGZkYTQ2YWQzMmQwIn0_"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": ["Analytics", "Generative AI", "Artificial Intelligence", "Machine Learning", "Building Modern Applications", "Compute"], "SessionType": "Keynote", "SessionID": "KEY001", "Tracks": "Keynote"}, "stats": [], "hiddenTags": "analytics,generative ai,artificial intelligence,machine learning,building modern applications,compute,__sandeep_du<PERSON>,__sirish__<PERSON><PERSON><PERSON><PERSON><PERSON>,__rafee__<PERSON><PERSON><PERSON><PERSON>,__ram<PERSON>__g<PERSON><PERSON><PERSON>,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OnlineKEY001", "canAddToWatchList": true}, {"id": "1_yamx8ogy", "name": "AWS\n  Summit Bengaluru: Technical Edition Keynote", "description": "What does it take to build anything you imagine in today's rapidly evolving tech landscape? Join us to watch the AWS Summit Bengaluru: Technical Edition keynote and discover how organizations are building from India for the world. In this technical keynote, we'll explore the transformative power of Generative AI through real-world implementations by innovators, who are pushing the boundaries of culturally-aware LLMs and developer productivity. Dive deep into AWS's custom silicon innovations with AWS Trainium and AWS Inferentia, seeing how Indian tech leaders like are leveraging these solutions to scale efficiently. Experience how technology is driving social impact across healthcare, education, and agriculture, while maintaining our commitment to responsible AI and sustainability. Through live demonstrations and customer stories, learn how AWS provides the building blocks for your next breakthrough innovation. Discover the tools and technologies shaping the future of work and innovation in India and beyond.", "type": "7", "updatedAt": **********, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_yamx8ogy/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Keynote", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6InRtZ3ljVFI1bUQrVVhTVDBhc2lFSnc9PSIsInZhbHVlIjoiem42bFowQW5Xb3IrUkVWa1hzVTZaS2xkKytUd3ROU0RPbmdqOElRa1VTdz0iLCJtYWMiOiI5YmIzYzM1ZDVkNTdmOGQ3ZThkYjcxNTNjZTdhYWI0MDU1MzEzYjZjNDU0OGU2MWQ1Y2ExNzlkYWU0NmRkY2VmIn0_", "name": "<PERSON><PERSON>", "title": "Director of Technology, APJ, AWS", "bio": "", "link": "/profile/eyJpdiI6InRtZ3ljVFI1bUQrVVhTVDBhc2lFSnc9PSIsInZhbHVlIjoiem42bFowQW5Xb3IrUkVWa1hzVTZaS2xkKytUd3ROU0RPbmdqOElRa1VTdz0iLCJtYWMiOiI5YmIzYzM1ZDVkNTdmOGQ3ZThkYjcxNTNjZTdhYWI0MDU1MzEzYjZjNDU0OGU2MWQ1Y2ExNzlkYWU0NmRkY2VmIn0_"}, {"id": "eyJpdiI6IlFPR1ZMU2ZwdUdxcEtQY1lXZGJmNlE9PSIsInZhbHVlIjoiVkw0YjJvUG5Ibk1nRVd0RUZkRVNuXC8zVm1lRGtaOTRFdU9FTWtva3NuZ2VHbjFPd0dmNWZ4T2hja0tXcWlINGEiLCJtYWMiOiJjMjYxMzQwZDEzYTIyMGIyYjk0MDZlMzMyNGQ3ZTI3MTkyNGM3ZWI1ZmExOGI0Zjk3NzkwM2I3NDMxNmI2M2RlIn0_", "name": "<PERSON><PERSON><PERSON>", "title": "Director, Solution Architects, AWS India", "bio": "", "link": "/profile/eyJpdiI6IlFPR1ZMU2ZwdUdxcEtQY1lXZGJmNlE9PSIsInZhbHVlIjoiVkw0YjJvUG5Ibk1nRVd0RUZkRVNuXC8zVm1lRGtaOTRFdU9FTWtva3NuZ2VHbjFPd0dmNWZ4T2hja0tXcWlINGEiLCJtYWMiOiJjMjYxMzQwZDEzYTIyMGIyYjk0MDZlMzMyNGQ3ZTI3MTkyNGM3ZWI1ZmExOGI0Zjk3NzkwM2I3NDMxNmI2M2RlIn0_"}, {"id": "eyJpdiI6Inozb1I2bzdpYnNQQmNBWWd3d2psMWc9PSIsInZhbHVlIjoiWitXYkNEazQ5Wk5VdlZIN0ZaSEVIVmtwVE1MYkpcLzk5RE5ZRXduK1VPSmNWMFZ0NnVKT0w4WkExeXo3NmhNNFoiLCJtYWMiOiIwMzM3NTZmMTA5Yzk3NWZmYzUwNTg4NjNhZWMwMjVmMTllOGI0NTE2NzAzNjE4MTc4YzEzZDVkYjFlNTNiNDQ5In0_", "name": "Dr. <PERSON><PERSON><PERSON><PERSON>", "title": "Co-Founder, Sarvam.ai", "bio": "", "link": "/profile/eyJpdiI6Inozb1I2bzdpYnNQQmNBWWd3d2psMWc9PSIsInZhbHVlIjoiWitXYkNEazQ5Wk5VdlZIN0ZaSEVIVmtwVE1MYkpcLzk5RE5ZRXduK1VPSmNWMFZ0NnVKT0w4WkExeXo3NmhNNFoiLCJtYWMiOiIwMzM3NTZmMTA5Yzk3NWZmYzUwNTg4NjNhZWMwMjVmMTllOGI0NTE2NzAzNjE4MTc4YzEzZDVkYjFlNTNiNDQ5In0_"}, {"id": "eyJpdiI6InJydWxxTDN2MG02UnNFOFA5WVorN2c9PSIsInZhbHVlIjoidmhvNTVzOFZsa1djOVp5M29TdVZuRlNLWkJ6NVF1UXByS0NzYXhIKzJsa2YwdkdFVnRUTkZMbEExTG1od2NDRCIsIm1hYyI6ImQzMDkzMjI3M2E4NTMyMjNmMDliOGU0NDg2NDE1NGQ5ZjgzODMyODcxMTVlNGFmMjVjNDc1M2QyZTVjYWYxYTkifQ__", "name": "<PERSON><PERSON><PERSON>", "title": "CTO & Head of Engineering, Razorpay", "bio": "", "link": "/profile/eyJpdiI6InJydWxxTDN2MG02UnNFOFA5WVorN2c9PSIsInZhbHVlIjoidmhvNTVzOFZsa1djOVp5M29TdVZuRlNLWkJ6NVF1UXByS0NzYXhIKzJsa2YwdkdFVnRUTkZMbEExTG1od2NDRCIsIm1hYyI6ImQzMDkzMjI3M2E4NTMyMjNmMDliOGU0NDg2NDE1NGQ5ZjgzODMyODcxMTVlNGFmMjVjNDc1M2QyZTVjYWYxYTkifQ__"}, {"id": "eyJpdiI6ImdTdFwvZ21aRmVXVGhHaUhvTDVvKzd3PT0iLCJ2YWx1ZSI6InpOY1NwcHpuR1ZhZktXZ0xSTExnbHczUWkzZ3MzOUZXRFVIWWN3Zm9Sd3M9IiwibWFjIjoiZDlkYjFlMGJmNTRlNWY2ODMwMjIwNGVmMmEwMGNlMmI4NDE4YmUxOGFiZWRhMDlmNjkwMTg0ZGE3ODBjNDE0YiJ9", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Co-Founder, Postman", "bio": "", "link": "/profile/eyJpdiI6ImdTdFwvZ21aRmVXVGhHaUhvTDVvKzd3PT0iLCJ2YWx1ZSI6InpOY1NwcHpuR1ZhZktXZ0xSTExnbHczUWkzZ3MzOUZXRFVIWWN3Zm9Sd3M9IiwibWFjIjoiZDlkYjFlMGJmNTRlNWY2ODMwMjIwNGVmMmEwMGNlMmI4NDE4YmUxOGFiZWRhMDlmNjkwMTg0ZGE3ODBjNDE0YiJ9"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": ["Generative AI", "Building Modern Applications", "Artificial Intelligence", "Machine Learning", "Serverless"], "SessionType": "Keynote", "SessionID": "KEY002", "Tracks": "Keynote"}, "stats": [], "hiddenTags": "generative ai,building modern applications,artificial intelligence,machine learning,serverless,__santanu__dutt,__satinder___pal_singh,__dr__pratyush___kumar,__mur<PERSON>_brahm<PERSON><PERSON>,__abhijit_kane,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OnlineKEY002", "canAddToWatchList": true}, {"id": "1_iyt64jpt", "name": "AWS  Summit Mumbai Keynote", "description": "The fusion of generative AI and cloud technology is opening up remarkable new frontiers in innovation. We invite you to join us to watch the keynote from AWS Summit Mumbai, where we explore AWS's distinctive position in today's AI landscape and trace the technological journey that has led us to this pivotal moment.\n\nExperience a comprehensive overview of the customer adoption pathway - from foundational infrastructure to advanced applications - with special emphasis on enterprise-critical implementations and how data serves as a competitive advantage. Be inspired by real-world examples showcasing innovative generative AI solutions built on AWS, along with proven approaches for developing secure, contemporary applications in the cloud.\n\nThis keynote is essential for both AI transformation enthusiasts and organizations looking to expand their cloud capabilities. You'll gain valuable practical insights and actionable strategies for success in the evolving digital landscape. Join us to be part of the convergence of AI and cloud computing that's reshaping the future of technology.\n\nDon't miss this opportunity to gain firsthand knowledge at the intersection of AI innovation and cloud excellence.", "type": "7", "updatedAt": 1748352442, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_iyt64jpt/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Keynote", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": 1750916700, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IkpFRkVKeStTaVdSazNcL082QjRnWXFRPT0iLCJ2YWx1ZSI6InZ1RWJ6NzA5S3g2ZW5OQVhyXC9Cc3JPaUJuNmNYRnFzV0w0bW5WR3llS3hRPSIsIm1hYyI6ImUzOGU3N2U2MjAxZWQxOTUyYjJiZDczMmJhOTU1OWQ1YTIyNjE2MTY1YjUzODc5NzdhNjZjOWYwYTAyNjVhYWQifQ__", "name": "<PERSON><PERSON>", "title": "President, India and South Asia, AWS", "bio": "", "link": "/profile/eyJpdiI6IkpFRkVKeStTaVdSazNcL082QjRnWXFRPT0iLCJ2YWx1ZSI6InZ1RWJ6NzA5S3g2ZW5OQVhyXC9Cc3JPaUJuNmNYRnFzV0w0bW5WR3llS3hRPSIsIm1hYyI6ImUzOGU3N2U2MjAxZWQxOTUyYjJiZDczMmJhOTU1OWQ1YTIyNjE2MTY1YjUzODc5NzdhNjZjOWYwYTAyNjVhYWQifQ__"}, {"id": "eyJpdiI6ImdrNUwyMUtoMTBqendCQXBFa1FNXC9nPT0iLCJ2YWx1ZSI6ImQrdW8xVVJoVVJjNU5xTm4rYVV2NWVBMFMxXC9mXC8rbVV0OExQV1JVbjMzR1d5ZkVLRGRRd2pQemhaUzlBemZvXC9hM3lWUFN1UXduSlpvMXJKakRPa01nPT0iLCJtYWMiOiIxOTc4ZmZkNzFlNjU4ZjM3YTAyNGQ5ZmExOGFjOWY4MGZhZTlkOWIxODYwZmYzMmFmZTc4ZDZiNjgwYTBiYzAyIn0_", "name": "<PERSON><PERSON><PERSON> (G2) <PERSON><PERSON><PERSON>", "title": "Vice President,  Database Services, AWS", "bio": "", "link": "/profile/eyJpdiI6ImdrNUwyMUtoMTBqendCQXBFa1FNXC9nPT0iLCJ2YWx1ZSI6ImQrdW8xVVJoVVJjNU5xTm4rYVV2NWVBMFMxXC9mXC8rbVV0OExQV1JVbjMzR1d5ZkVLRGRRd2pQemhaUzlBemZvXC9hM3lWUFN1UXduSlpvMXJKakRPa01nPT0iLCJtYWMiOiIxOTc4ZmZkNzFlNjU4ZjM3YTAyNGQ5ZmExOGFjOWY4MGZhZTlkOWIxODYwZmYzMmFmZTc4ZDZiNjgwYTBiYzAyIn0_"}, {"id": "eyJpdiI6IndxYlhpWUN0M3NwUVZuOHY5ZFJTY2c9PSIsInZhbHVlIjoiZXFWYTdDZTBCUmI4XC90aFBNR3B3TEFZbHVmWGxQZ2FLUWdLRzVDM0l2bVU9IiwibWFjIjoiNWJjMWU5MWY0MjM1ZDU0YzI1MTAwMzg1NzFjOTZkYTFjNzg0MTQ1NDhmYzk3MGViNDMyZmI3ZjU0MDAyZDhiMSJ9", "name": "Dr. <PERSON><PERSON>", "title": "Head of Digital Enterprise and Information & Cyber Security, Maruti Suzuki India Limited", "bio": "", "link": "/profile/eyJpdiI6IndxYlhpWUN0M3NwUVZuOHY5ZFJTY2c9PSIsInZhbHVlIjoiZXFWYTdDZTBCUmI4XC90aFBNR3B3TEFZbHVmWGxQZ2FLUWdLRzVDM0l2bVU9IiwibWFjIjoiNWJjMWU5MWY0MjM1ZDU0YzI1MTAwMzg1NzFjOTZkYTFjNzg0MTQ1NDhmYzk3MGViNDMyZmI3ZjU0MDAyZDhiMSJ9"}, {"id": "eyJpdiI6IlJDTDlUS1wvdEJYZDkwaU92WmZXOTR3PT0iLCJ2YWx1ZSI6Im1UeWYzbE13cWR3YzhvNG5LWFQycm82UjhCUHJtUjFZTWFGZ3p1K1BYTmprZGJJNnU5UjRRV1lDRWNrMUVoMGgiLCJtYWMiOiJhYTNkNjgyZDEzYTlmYzQ4N2FkYTVjMzI5NmRlMGQxZTliMzIzZjRlNzlmNDJiM2U1YzU5MjEyZTNiYTY1NzRmIn0_", "name": "<PERSON><PERSON>", "title": "CEO, Comviva", "bio": "", "link": "/profile/eyJpdiI6IlJDTDlUS1wvdEJYZDkwaU92WmZXOTR3PT0iLCJ2YWx1ZSI6Im1UeWYzbE13cWR3YzhvNG5LWFQycm82UjhCUHJtUjFZTWFGZ3p1K1BYTmprZGJJNnU5UjRRV1lDRWNrMUVoMGgiLCJtYWMiOiJhYTNkNjgyZDEzYTlmYzQ4N2FkYTVjMzI5NmRlMGQxZTliMzIzZjRlNzlmNDJiM2U1YzU5MjEyZTNiYTY1NzRmIn0_"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": ["Analytics", "Generative AI", "Artificial Intelligence", "Machine Learning", "Building Modern Applications", "Compute"], "SessionType": "Keynote", "SessionID": "KEY003", "Tracks": "Keynote"}, "stats": [], "hiddenTags": "analytics,generative ai,artificial intelligence,machine learning,building modern applications,compute,__sandeep_dutta,__ganapathy__g2___<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,__dr__tapan_sahoo,__raj<PERSON>__chand<PERSON><PERSON>,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OnlineKEY003", "canAddToWatchList": true}, {"id": "1_ass7x75l", "name": "Build without limits with AWS Cloud infrastructure", "description": "Organizations of all sizes are looking to solve big challenges, turn ideas into reality, and innovate faster. This requires a secure and reliable infrastructure that delivers high performance at low costs and scales without limits, while offering technology choices tailored to an organization’s specific needs. In this session, learn how builders are using AWS Cloud infrastructure to create and run critical applications that meet customer and business needs. Explore AWS innovations, starting from the silicon and extending to data centers across the globe. Discover how AWS can accelerate innovation, improve efficiency, and support global scale for your organization.", "type": "7", "updatedAt": 1748352516, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_ass7x75l/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Building for scale", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6ImlVMjJRazdVVjI0S2x1aTh5anZMenc9PSIsInZhbHVlIjoiSHN0Z2ZZUjhpVURsaTNxV0UyY00rY2xFbTU5XC9wUDQxY1JDYmxadjNQU3VRVlNmNU1PelN1VFc1K2cxZHAyMloiLCJtYWMiOiI5MjNhMGE4YTA3Mzc5MTUxZDBjOTlhN2I3MTVmZWE0ZDNmNjgyMjUyY2RmYTJhZWQ4Y2QwM2E5OGYzYTVhMWZmIn0_", "name": "<PERSON><PERSON><PERSON>", "title": "Senior Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6ImlVMjJRazdVVjI0S2x1aTh5anZMenc9PSIsInZhbHVlIjoiSHN0Z2ZZUjhpVURsaTNxV0UyY00rY2xFbTU5XC9wUDQxY1JDYmxadjNQU3VRVlNmNU1PelN1VFc1K2cxZHAyMloiLCJtYWMiOiI5MjNhMGE4YTA3Mzc5MTUxZDBjOTlhN2I3MTVmZWE0ZDNmNjgyMjUyY2RmYTJhZWQ4Y2QwM2E5OGYzYTVhMWZmIn0_"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": ["Scale on AWS", "Compute"], "SessionType": "Breakout Session", "SessionID": "OET401", "Tracks": "Building for scale"}, "stats": [], "hiddenTags": "scale on aws,compute,breakout session,__sathish__ha<PERSON>haran,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET401", "canAddToWatchList": true}, {"id": "1_cm8v9f72", "name": "Automate Java app upgrades & accelerate innovation with generative AI", "description": "Amazon Q Developer’s agent for code transformation automates the end-to-end process of upgrading and transforming code. Reduce the time and costs associated with modernizing applications, unlock previously cost-prohibitive and cumbersome modernization opportunities, and save customers months or even years of effort. By automating undifferentiated upgrade and modernization tasks, customers can enhance application performance and security and accelerate innovation. Join this session to learn how to take your application modernization to the next level.", "type": "7", "updatedAt": 1748352563, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_cm8v9f72/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 300 - Advanced,Future of Developers", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IjVGZzVmZ1pZZlZnNnByVDBcL1BZOFd3PT0iLCJ2YWx1ZSI6InE5VzZxXC9yN1ZNREZ3UE9FaXJEYmNjc2tWYW5NTTlzMmJEVzdyemNmam9BPSIsIm1hYyI6IjhhMjM0ZWQ4Njg1NzgwMDVjNjAwMDU1YjVhMzYxZTQ5NjQ2Y2JiNzEwY2JmM2Q1NmQyMTIwNDE0NmI2Nzk0NzYifQ__", "name": "<PERSON><PERSON><PERSON>", "title": "Senior Solutions Architect, Developer Specialist, AWS India", "bio": "", "link": "/profile/eyJpdiI6IjVGZzVmZ1pZZlZnNnByVDBcL1BZOFd3PT0iLCJ2YWx1ZSI6InE5VzZxXC9yN1ZNREZ3UE9FaXJEYmNjc2tWYW5NTTlzMmJEVzdyemNmam9BPSIsIm1hYyI6IjhhMjM0ZWQ4Njg1NzgwMDVjNjAwMDU1YjVhMzYxZTQ5NjQ2Y2JiNzEwY2JmM2Q1NmQyMTIwNDE0NmI2Nzk0NzYifQ__"}, {"id": "eyJpdiI6IldpQ0M5UUxQdWlaNDFBT1VwdFI0eFE9PSIsInZhbHVlIjoicGNrVUxzYXBnbDZSS0cwaE1IT0ZsakdzYk9BS2c5dkRaWTdvd2dpb2xGQT0iLCJtYWMiOiI0YTkwNDJlZTdhOWJmMTM4ZGNkZGEyOTg2NWQxYWM4YzcyNGMxMDI0OWRkY2Y5NTAwM2YyNzdkMTM2MTRiMTExIn0_", "name": "<PERSON><PERSON>", "title": "Technical Account Manager, AWS India", "bio": "", "link": "/profile/eyJpdiI6IldpQ0M5UUxQdWlaNDFBT1VwdFI0eFE9PSIsInZhbHVlIjoicGNrVUxzYXBnbDZSS0cwaE1IT0ZsakdzYk9BS2c5dkRaWTdvd2dpb2xGQT0iLCJtYWMiOiI0YTkwNDJlZTdhOWJmMTM4ZGNkZGEyOTg2NWQxYWM4YzcyNGMxMDI0OWRkY2Y5NTAwM2YyNzdkMTM2MTRiMTExIn0_"}], "customData": {"SessionLevel": "Level 300 - Advanced", "Topics": ["Generative AI", "Artificial Intelligence"], "SessionType": "Breakout Session", "SessionID": "OET601", "Tracks": "Future of Developers"}, "stats": [], "hiddenTags": "generative ai,artificial intelligence,breakout session,__k<PERSON><PERSON>_singh,__sandip__samanta,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET601", "canAddToWatchList": true}, {"id": "1_2gva1cwv", "name": "Data foundation for analytics and AI: From data to outcomes at scale", "description": "Data decision-makers face a transformative challenge as the boundaries between analytics and AI blur, and teams need to move effortlessly between analyzing historical patterns, predicting future scenarios, and deploying AI applications. However, some organizations remain constrained by fragmented workflows, data silos, operational overhead, and limitations of scale and performance. In this session, learn how AWS’s integrated storage, database, and analytics capabilities create a unified, high performance, and secure data foundation where data scientists, analysts, and engineers can collaborate seamlessly across the entire data lifecycle to power analytics and AI outcomes for the business.", "type": "7", "updatedAt": 1748352469, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_2gva1cwv/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,AWS for data", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6Ik5zTkNHZ2Y3cklvcmxWWmlTXC9xUGZnPT0iLCJ2YWx1ZSI6IkpCNXdERnFsMkpYMzhHS2NvczNEZFFqOWU1STdjOUF5UDE1NFFySCtzNjA9IiwibWFjIjoiYjA4Y2NiMjQ3ZDk5ODlmMzFiMDUzNWM3ZGI5OWFkMzhlNDg5NDUyYjViYTUwOTgxODczMWJiN2QzMjY2NDRhYSJ9", "name": "<PERSON>", "title": "Analytics Specialist, AWS India", "bio": "", "link": "/profile/eyJpdiI6Ik5zTkNHZ2Y3cklvcmxWWmlTXC9xUGZnPT0iLCJ2YWx1ZSI6IkpCNXdERnFsMkpYMzhHS2NvczNEZFFqOWU1STdjOUF5UDE1NFFySCtzNjA9IiwibWFjIjoiYjA4Y2NiMjQ3ZDk5ODlmMzFiMDUzNWM3ZGI5OWFkMzhlNDg5NDUyYjViYTUwOTgxODczMWJiN2QzMjY2NDRhYSJ9"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": ["Big Data", "Databases", "Analytics", "Storage"], "SessionType": "Breakout Session", "SessionID": "OET201", "Tracks": "AWS for data"}, "stats": [], "hiddenTags": "big data,databases,analytics,storage,breakout session,__ravi_kompella,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET201", "canAddToWatchList": true}, {"id": "1_llpc6w6q", "name": "AWS security at scale: From development to production", "description": "Discover how to enhance your cloud security posture across the entire development and production lifecycle. This session explores the AWS comprehensive security approach, covering identification, prevention, detection, response, and remediation. Learn to integrate security-by-design principles throughout your development process and use advanced detection and response capabilities. Explore how generative AI enhances security analysis and automates operations. Gain insights into building resilient architectures that evolve with emerging threats, enabling you to create more secure, scalable cloud environments.", "type": "7", "updatedAt": 1748352540, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_llpc6w6q/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Security compliance & resilience", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6ImJEXC9wd3pFOVR0TU1DazR1aHNSMklBPT0iLCJ2YWx1ZSI6IjN5MUlxM2hMaThEMmZya3l3V0pZaHZNMTh5dEN0d3NPN0txbFU4WlE3MndzdERWeDhob3hYa3phRlwvOUFNWHB3IiwibWFjIjoiN2NmYjE4MWJlODI3YTRiYWJjMDA0MThjNWNlNTY3OTdjZGQ2OTYxZTdjOGQzYWNkYzI2YTA1OTJhZTQxZjIxMCJ9", "name": "Paramanand Mallik", "title": "Security Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6ImJEXC9wd3pFOVR0TU1DazR1aHNSMklBPT0iLCJ2YWx1ZSI6IjN5MUlxM2hMaThEMmZya3l3V0pZaHZNMTh5dEN0d3NPN0txbFU4WlE3MndzdERWeDhob3hYa3phRlwvOUFNWHB3IiwibWFjIjoiN2NmYjE4MWJlODI3YTRiYWJjMDA0MThjNWNlNTY3OTdjZGQ2OTYxZTdjOGQzYWNkYzI2YTA1OTJhZTQxZjIxMCJ9"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": "Security and Resilience", "SessionType": "Breakout Session", "SessionID": "OET501", "Tracks": "Security compliance & resilience"}, "stats": [], "hiddenTags": "security and resilience,breakout session,__paramanand_mallik,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET501", "canAddToWatchList": true}, {"id": "1_f4gu0cm8", "name": "Gen AI in action: from POC to business value", "description": "More global leaders like Bundesliga and Trellix are taking their generative AI applications to production and realizing business impact through increased innovation, productivity, and cost savings. How are they achieving these benefits and what’s their path from POC to production? Through training, professional services experts, and comprehensive generative AI offerings, AWS provides trusted guidance and tools for customers to scale with confidence. In this session, learn how new services and models like Amazon Nova, Amazon Bedrock, and Amazon Q are delivering business value across use cases to enhance customer engagement and boost employee productivity.", "type": "7", "updatedAt": 1748520618, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_f4gu0cm8/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Generative AI on AWS", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6Ilk1ejhxOEErZmNodnB3RmtoUnY2b2c9PSIsInZhbHVlIjoiUFN2T3gxZWhLYUYrWE9DNUZ5Qjl3VFlKT2hSTFJRT1c4ZlJOYkNOWmc2U0xSbUNvRGZCaTVBbVBRV0dOTXNoTSIsIm1hYyI6IjgzNzNmMjU0MmQ5NjA1YzNhMDU2YzhmOTQyZGMzMDYwZGU5ZmU5NTBiM2JhOWY0ZmM5ZWQyOTNkNDNmNjEyM2IifQ__", "name": "<PERSON><PERSON>", "title": "Senior Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6Ilk1ejhxOEErZmNodnB3RmtoUnY2b2c9PSIsInZhbHVlIjoiUFN2T3gxZWhLYUYrWE9DNUZ5Qjl3VFlKT2hSTFJRT1c4ZlJOYkNOWmc2U0xSbUNvRGZCaTVBbVBRV0dOTXNoTSIsIm1hYyI6IjgzNzNmMjU0MmQ5NjA1YzNhMDU2YzhmOTQyZGMzMDYwZGU5ZmU5NTBiM2JhOWY0ZmM5ZWQyOTNkNDNmNjEyM2IifQ__"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": ["Artificial Intelligence", "Generative AI", "Machine Learning"], "SessionType": "Breakout Session", "SessionID": "OET101", "Tracks": "Generative AI on AWS"}, "stats": [], "hiddenTags": "artificial intelligence,generative ai,machine learning,breakout session,__arun__nal<PERSON>_<PERSON><PERSON><PERSON><PERSON>a,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET101", "canAddToWatchList": true}, {"id": "1_ih1lpiwi", "name": "How AWS migration & modernization accelerate business transformation", "description": "Maximize the value of your legacy systems and accelerate your organization’s journey to the cloud. Discover how AWS is revolutionizing migration and modernization, turning complex challenges into opportunities. Explore cutting-edge services that redefine digital transformation, from AI-powered discovery tools to automated refactoring solutions. Learn how industry leaders use these tools to reduce costs, eliminate licensing issues, and speed up innovation. Whether you’re a business decision-maker, IT leader, or technical professional, this session will equip you with strategies to drive your organization’s cloud transformation. Join us to transform your legacy systems into a springboard for innovation with AWS.", "type": "7", "updatedAt": 1748352493, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_ih1lpiwi/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Migration & modernization", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6InhFZFFQZDdmT280M0R2aEpUelhGMXc9PSIsInZhbHVlIjoiMTM0VTZNdmd6OTBMVmJWM0JZc2FpNHZ2TEJvWkg4YmZXVk5jU1dpdGc0Q1wvWEd3d2pJWUtrN1BzajdvUGhKUmoiLCJtYWMiOiIxZjdkMWJmM2Y0N2IyZmUyZDA5MWIyNzc0YzAyNWM2ZTY3NTdhYTYyYjRiMDM3YWFkMTRkZmQ2NWRmMThhNTdiIn0_", "name": "<PERSON>", "title": "Senior Technical Customer Solutions Manager, AWS India", "bio": "", "link": "/profile/eyJpdiI6InhFZFFQZDdmT280M0R2aEpUelhGMXc9PSIsInZhbHVlIjoiMTM0VTZNdmd6OTBMVmJWM0JZc2FpNHZ2TEJvWkg4YmZXVk5jU1dpdGc0Q1wvWEd3d2pJWUtrN1BzajdvUGhKUmoiLCJtYWMiOiIxZjdkMWJmM2Y0N2IyZmUyZDA5MWIyNzc0YzAyNWM2ZTY3NTdhYTYyYjRiMDM3YWFkMTRkZmQ2NWRmMThhNTdiIn0_"}, {"id": "eyJpdiI6IlAwOHpFUHF4NW5ib1dJb3JsckYwcXc9PSIsInZhbHVlIjoiWlRqRU5HQjRMd3YyOTlONnlqaVBIbFhDY3BXMjBPbXRKT0I2c25TTnNSST0iLCJtYWMiOiIxYmI3YWYzYjZjMTA0MjFjNzk4ZWE4NzljYThjMThjOTNkOGZjMWMzMWRkMGQ4Yjc0N2Q3ZTcwNzc5MjRkM2VkIn0_", "name": "<PERSON><PERSON>h PK", "title": "Senior Technical Customer Solutions Manager, AWS India", "bio": "", "link": "/profile/eyJpdiI6IlAwOHpFUHF4NW5ib1dJb3JsckYwcXc9PSIsInZhbHVlIjoiWlRqRU5HQjRMd3YyOTlONnlqaVBIbFhDY3BXMjBPbXRKT0I2c25TTnNSST0iLCJtYWMiOiIxYmI3YWYzYjZjMTA0MjFjNzk4ZWE4NzljYThjMThjOTNkOGZjMWMzMWRkMGQ4Yjc0N2Q3ZTcwNzc5MjRkM2VkIn0_"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Application Modernization", "Building Modern Applications", "Migration and Modernization"], "SessionType": "Breakout Session", "SessionID": "OET301", "Tracks": "Migration & modernization"}, "stats": [], "hiddenTags": "application modernization,building modern applications,migration and modernization,breakout session,__ravi__<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,__magesh_pk,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET301", "canAddToWatchList": true}, {"id": "1_gmpacvim", "name": "Using multiple agents for scalable generative AI applications", "description": "Join this session to learn how you can transform your application development support system using Amazon Bedrock multi-agent collaboration with better planning and communication among agents for your organizations. Learn how to create specialized agents for different tasks like account management, repos, pipeline management, and more to help  developers go faster. Explore the significant productivity gains and efficiency improvements achieved across organizations.", "type": "7", "updatedAt": **********, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_gmpacvim/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 300 - Advanced,Future of Developers", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6Ik05eWY1OTRiQVpmcmZQQ1Q5SkRCYWc9PSIsInZhbHVlIjoiZUJURlU3NFBIaWwzZXc3clA0blNnaUpySjdNSTEzMHp1cE9KVGdvM0tBSXZDdEdNaSt5NTRPZVA2VDFhUzRqbSIsIm1hYyI6IjI1MGZmNWZjYjQxZTAyZGU5OTQwMjkxYzFmN2FmODc0MDNkNmJkNjgwNDI0MzU5ZWFlMGEwZWM5OWIyMTY5MTYifQ__", "name": "<PERSON><PERSON><PERSON>", "title": "Developer Specialist, Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6Ik05eWY1OTRiQVpmcmZQQ1Q5SkRCYWc9PSIsInZhbHVlIjoiZUJURlU3NFBIaWwzZXc3clA0blNnaUpySjdNSTEzMHp1cE9KVGdvM0tBSXZDdEdNaSt5NTRPZVA2VDFhUzRqbSIsIm1hYyI6IjI1MGZmNWZjYjQxZTAyZGU5OTQwMjkxYzFmN2FmODc0MDNkNmJkNjgwNDI0MzU5ZWFlMGEwZWM5OWIyMTY5MTYifQ__"}], "customData": {"SessionLevel": "Level 300 - Advanced", "Topics": ["Generative AI", "Artificial Intelligence"], "SessionType": "Breakout Session", "SessionID": "OET602", "Tracks": "Future of Developers"}, "stats": [], "hiddenTags": "generative ai,artificial intelligence,breakout session,__anurag__vikram_singh,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET602", "canAddToWatchList": true}, {"id": "1_wtikvjj6", "name": "Using multiple agents for scalable generative AI applications", "description": "Join this session to learn how to transform application development support systems using Amazon Bedrock multi-agent collaboration with better planning and communication among agents. Learn how to create specialized agents for different tasks to optimize efficiency of your workload. Explore the significant productivity gains and efficiency improvements achieved across organizations.", "type": "7", "updatedAt": 1748352450, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_wtikvjj6/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Generative AI on AWS", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IjlCTTlIbUpuNVFOY2VaYVI0WHlKOGc9PSIsInZhbHVlIjoiSmlvTW5IRWNVUWIrUDlGdzlKRTdPdE1mWGFYVzlnd3JscEZZNVdrTVAwK1wvUUVpMXRzMGhiYXA5c0pzQ0FUengiLCJtYWMiOiI4OWE0ODNhOThiOWZkMzFkNGM1YTYyZmYxOWY4OTVkOWRlNmQ0ZmRkMzQzOGQxYjkxMzI0ZWU0Yzg5NTU4NTM1In0_", "name": "<PERSON><PERSON>", "title": "Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6IjlCTTlIbUpuNVFOY2VaYVI0WHlKOGc9PSIsInZhbHVlIjoiSmlvTW5IRWNVUWIrUDlGdzlKRTdPdE1mWGFYVzlnd3JscEZZNVdrTVAwK1wvUUVpMXRzMGhiYXA5c0pzQ0FUengiLCJtYWMiOiI4OWE0ODNhOThiOWZkMzFkNGM1YTYyZmYxOWY4OTVkOWRlNmQ0ZmRkMzQzOGQxYjkxMzI0ZWU0Yzg5NTU4NTM1In0_"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Artificial Intelligence", "Generative AI", "Machine Learning"], "SessionType": "Breakout Session", "SessionID": "OET102", "Tracks": "Generative AI on AWS"}, "stats": [], "hiddenTags": "artificial intelligence,generative ai,machine learning,breakout session,__pavan_kumar_rao_navule,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET102", "canAddToWatchList": true}, {"id": "1_dsn6znrn", "name": "Architect your applications for unmatched scalability and resilience with innovations in Amazon Aurora", "description": "With an innovative architecture that decouples compute from storage and advanced features like Amazon Aurora Global Database and low-latency read replicas, Aurora reimagines what it means to be a relational database. Aurora is a modern database service offering unparalleled performance and high availability at scale with full open source MySQL and PostgreSQL compatibility. Join this session to understand how you can leverage the exciting new features that Aurora offers to build scalable resilient high performance applications", "type": "7", "updatedAt": 1748352473, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_dsn6znrn/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,AWS for data", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IlVaUnZ6TDJyMCt4VzQ4azYzM2d3dkE9PSIsInZhbHVlIjoiS3JpSGJON1wvSWV6UW1hNUdcL3l1ZkZNMmk4NHlYNVFNR3lpZ0FmbktPR2tNPSIsIm1hYyI6Ijc5NWMzOTczOGU2ZGRlY2FiMGRhYmUzNzBhYWQ4NTZlZWViZjA1ZWU5ZDE1NzdjZDdlMTFlNzc5YjRmYWQzYjQifQ__", "name": "Deep <PERSON>", "title": "Database Specialist Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6IlVaUnZ6TDJyMCt4VzQ4azYzM2d3dkE9PSIsInZhbHVlIjoiS3JpSGJON1wvSWV6UW1hNUdcL3l1ZkZNMmk4NHlYNVFNR3lpZ0FmbktPR2tNPSIsIm1hYyI6Ijc5NWMzOTczOGU2ZGRlY2FiMGRhYmUzNzBhYWQ4NTZlZWViZjA1ZWU5ZDE1NzdjZDdlMTFlNzc5YjRmYWQzYjQifQ__"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Big Data", "Databases", "Analytics", "Storage"], "SessionType": "Breakout Session", "SessionID": "OET202", "Tracks": "AWS for data"}, "stats": [], "hiddenTags": "big data,databases,analytics,storage,breakout session,__deep_dey,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET202", "canAddToWatchList": true}, {"id": "1_xb1dsfhm", "name": "Innovations in AWS detections and response", "description": "Join this session to learn about the latest advancements and recent AWS launches in detection and response. This session focuses on practical use cases, such as threat detection, workload and data protection, automated and continual vulnerability management, centralized monitoring, continuous cloud security posture management, unified security data management, investigations and response, and generative AI. Gain a deeper understanding of how you can seamlessly integrate AWS detection and response services to help protect your workloads at scale, enhance your security posture, and streamline security operations across your entire AWS environment.", "type": "7", "updatedAt": 1748352544, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_xb1dsfhm/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Security compliance & resilience", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IkRqcUpjcHFVcUxMK3I5NFhKMDBsbkE9PSIsInZhbHVlIjoiak0zTTBuODk5ZWgySEo1NnNnWCtXZkswcUlXOWxNZUVyVnBFcHFiTWNJUT0iLCJtYWMiOiJmOTk4YWMwODA4MjQ4ODNiMDRmODg0YTUxZmY3NGI2ZGM1NmE1ZTJiNDZiZWFkZTczMTBlZjU2MTVkOTI4ZWEwIn0_", "name": "<PERSON><PERSON><PERSON>", "title": "Technical Account Manager, AWS India", "bio": "", "link": "/profile/eyJpdiI6IkRqcUpjcHFVcUxMK3I5NFhKMDBsbkE9PSIsInZhbHVlIjoiak0zTTBuODk5ZWgySEo1NnNnWCtXZkswcUlXOWxNZUVyVnBFcHFiTWNJUT0iLCJtYWMiOiJmOTk4YWMwODA4MjQ4ODNiMDRmODg0YTUxZmY3NGI2ZGM1NmE1ZTJiNDZiZWFkZTczMTBlZjU2MTVkOTI4ZWEwIn0_"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": "Security and Resilience", "SessionType": "Breakout Session", "SessionID": "OET502", "Tracks": "Security compliance & resilience"}, "stats": [], "hiddenTags": "security and resilience,breakout session,__sha<PERSON>_<PERSON><PERSON><PERSON>,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET502", "canAddToWatchList": true}, {"id": "1_dxb1p6h3", "name": "Scaling generative AI workloads with efficient model choice", "description": "As you build, deploy, and scale generative AI applications, using and managing the right set of models for the outcomes you desire becomes key. Amazon Bedrock is introducing several features designed to help you find the right models and help you enhance cost-efficiency while maintaining world-class performance and accuracy. Attend this session to learn about Amazon Bedrock Marketplace, Intelligent Prompt Routing, and Model Distillation.", "type": "7", "updatedAt": **********, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_dxb1p6h3/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Building for scale", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6InJleWtiSGs2NUx4VnlWbHJhUTFwbGc9PSIsInZhbHVlIjoiblRQenA0T1daelA3Ynh1TFhTMkJ1MHlhcnVya1BkUHZWb2M1c25tU3MyZz0iLCJtYWMiOiJlNWQ2N2VhNWQyNTcyMzRjODY3Mjg1OGRlODI1YzJlZmNhNzk3ZWY4N2ZkYWU3NTgyNzI2ZDc5MWI1ODMyN2EzIn0_", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Principal Technical Account Manager, AWS India", "bio": "", "link": "/profile/eyJpdiI6InJleWtiSGs2NUx4VnlWbHJhUTFwbGc9PSIsInZhbHVlIjoiblRQenA0T1daelA3Ynh1TFhTMkJ1MHlhcnVya1BkUHZWb2M1c25tU3MyZz0iLCJtYWMiOiJlNWQ2N2VhNWQyNTcyMzRjODY3Mjg1OGRlODI1YzJlZmNhNzk3ZWY4N2ZkYWU3NTgyNzI2ZDc5MWI1ODMyN2EzIn0_"}, {"id": "eyJpdiI6ImtyM1wvam9PdWlra29oanhvdktlUkZ3PT0iLCJ2YWx1ZSI6Ik9cL1wvaktRcWhSZURZaEkzOE5YSnBlaThwT2FIcER1QWxicUk5cW1KbVJHUTVoY3NVTWpock9nTko5aWNENm1tXC8iLCJtYWMiOiJjY2QyN2E3MTI2Y2U3NTFjMGEwYTdkYzUwOTZiMmJmYmQ2ZDQ2ZGI0OGZkZmQxMmE2ZmVhZDc0NWFjYTBiNzdlIn0_", "name": "<PERSON><PERSON><PERSON>", "title": "Principal Technical Account Manager, AWS India", "bio": "", "link": "/profile/eyJpdiI6ImtyM1wvam9PdWlra29oanhvdktlUkZ3PT0iLCJ2YWx1ZSI6Ik9cL1wvaktRcWhSZURZaEkzOE5YSnBlaThwT2FIcER1QWxicUk5cW1KbVJHUTVoY3NVTWpock9nTko5aWNENm1tXC8iLCJtYWMiOiJjY2QyN2E3MTI2Y2U3NTFjMGEwYTdkYzUwOTZiMmJmYmQ2ZDQ2ZGI0OGZkZmQxMmE2ZmVhZDc0NWFjYTBiNzdlIn0_"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": ["Generative AI", "Scale on AWS"], "SessionType": "Breakout Session", "SessionID": "OET402", "Tracks": "Building for scale"}, "stats": [], "hiddenTags": "generative ai,scale on aws,breakout session,__saurabh__garg,___karthik__chemud<PERSON><PERSON>,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET402", "canAddToWatchList": true}, {"id": "1_klkgspw4", "name": "Event-driven architectures with strangler and leave/layer patterns", "description": "Unlock the power of event-driven architecture (EDA) to modernize your existing applications with the strangler and leave/layer patterns. In this session, learn how to leverage Amazon EventBridge, AWS Lambda, and microservices to increase agility, resiliency, and security while reducing technical debt. Discover how to decompose monolithic applications into microservices seamlessly, enabling faster innovation, scalability, and maintainability.", "type": "7", "updatedAt": 1748352497, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_klkgspw4/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Migration & modernization", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6Imw0dVBBdzhYdVNyS1NvSWhSVmdnMEE9PSIsInZhbHVlIjoiTFduWmZTdzBqdmlwd1U3UWZPVGxsc2o0RVkzd2xvUStCNklkOHl6bTBKYlBVTm15b0psSFY0N3JLMTA3bDlGSiIsIm1hYyI6IjE3ZTQzNWQ4YmNiNmUwMmNhZDU5Njk3ZDkwNmY4OGVmZjVmNjMzMDlmODIyZGQ0YWVkNTg5MGMzZTUwMDk5ZTUifQ__", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Senior Delivery Consultant, AWS Professional Services India", "bio": "", "link": "/profile/eyJpdiI6Imw0dVBBdzhYdVNyS1NvSWhSVmdnMEE9PSIsInZhbHVlIjoiTFduWmZTdzBqdmlwd1U3UWZPVGxsc2o0RVkzd2xvUStCNklkOHl6bTBKYlBVTm15b0psSFY0N3JLMTA3bDlGSiIsIm1hYyI6IjE3ZTQzNWQ4YmNiNmUwMmNhZDU5Njk3ZDkwNmY4OGVmZjVmNjMzMDlmODIyZGQ0YWVkNTg5MGMzZTUwMDk5ZTUifQ__"}, {"id": "eyJpdiI6IllPMEJzWWoxVXNlWGdSNDFFXC9jTlVBPT0iLCJ2YWx1ZSI6ImhWS05ZTGlnR0drVGgyQTdiOWwyWU1WN3ZLcENyY29aMkM5NDlQZ1JvVG82dGFjTUMxaDVkXC96ZVpkNzN6bDJGIiwibWFjIjoiMThmZTc1YTgzY2I5NThjNWJjZTQ2MjE4YTQxMDM5MjQyOGNjZWExZWNjZmYyN2M4OGYzODJkYTM2MzlkZTU3YyJ9", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6IllPMEJzWWoxVXNlWGdSNDFFXC9jTlVBPT0iLCJ2YWx1ZSI6ImhWS05ZTGlnR0drVGgyQTdiOWwyWU1WN3ZLcENyY29aMkM5NDlQZ1JvVG82dGFjTUMxaDVkXC96ZVpkNzN6bDJGIiwibWFjIjoiMThmZTc1YTgzY2I5NThjNWJjZTQ2MjE4YTQxMDM5MjQyOGNjZWExZWNjZmYyN2M4OGYzODJkYTM2MzlkZTU3YyJ9"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Application Modernization", "Building Modern Applications", "Migration and Modernization"], "SessionType": "Breakout Session", "SessionID": "OET302", "Tracks": "Migration & modernization"}, "stats": [], "hiddenTags": "application modernization,building modern applications,migration and modernization,breakout session,__app<PERSON><PERSON><PERSON>_bagali,__<PERSON><PERSON><PERSON>_b<PERSON><PERSON><PERSON>,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET302", "canAddToWatchList": true}, {"id": "1_x5xd6qe0", "name": "Hybrid cloud with Nutanix on AWS: A platform for Apps Modernization", "description": "Would you like to move to the cloud in hours vs. months or years, while reducing your costs and improving effectiveness? Join us to hear how customers have modernized with AWS and Nutanix to create a modern hybrid cloud, all while lowering their costs with seamless AWS integration. Discover our unified management from edge to core to cloud and how you can accelerate application transformation.", "type": "7", "updatedAt": 1748352501, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_x5xd6qe0/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Migration & modernization", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": 1750924800, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IjJ5a091WjFBRUR2bDNVSXgzUnBIaEE9PSIsInZhbHVlIjoiK1MxQURNQXE0SlFqc2V3SnFvU1liYithOGc5YUVTVG4xdUF1V3dCa3VaWT0iLCJtYWMiOiIxMzQ0NmQ3OWI1M2Q1NGM2ODZjZjI1OTZjYmEzOTViYzVjNjVjMTA3YWI5MDFiZGVjM2MyNzU5ZWRkNWJkMTRhIn0_", "name": "<PERSON><PERSON><PERSON>", "title": "Country Pre-Sales Director, Nutanix", "bio": "", "link": "/profile/eyJpdiI6IjJ5a091WjFBRUR2bDNVSXgzUnBIaEE9PSIsInZhbHVlIjoiK1MxQURNQXE0SlFqc2V3SnFvU1liYithOGc5YUVTVG4xdUF1V3dCa3VaWT0iLCJtYWMiOiIxMzQ0NmQ3OWI1M2Q1NGM2ODZjZjI1OTZjYmEzOTViYzVjNjVjMTA3YWI5MDFiZGVjM2MyNzU5ZWRkNWJkMTRhIn0_"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Application Modernization", "Building Modern Applications", "Migration and Modernization"], "SessionType": "Breakout Session", "SessionID": "OET303", "Tracks": "Migration & modernization"}, "stats": [], "hiddenTags": "application modernization,building modern applications,migration and modernization,breakout session,__premkumar__jg,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET303", "canAddToWatchList": true}, {"id": "1_s74xhvix", "name": "Accelerate multistep SDLC tasks with Amazon Q Developer Agent", "description": "While previous AI assistants focus on code generation with close human guidance, Amazon Q Developer has a unique capability called Amazon Q Developer Agent that can use reasoning and planning capabilities to perform multi-step tasks beyond code generation with minimal human intervention. Its agent for software development can solve complex tasks that go beyond code suggestions, from building application features to automatic code reviews, unit tests, and documentation generation. In this session, discover how the new agent capabilities help developers go from planning to building new features faster.", "type": "7", "updatedAt": 1748352571, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_s74xhvix/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 300 - Advanced,Future of Developers", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": 1750924800, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6Im5oY0FnZUt2TFZLMHBuRjNmNjg4Qmc9PSIsInZhbHVlIjoidUxQYithZWZLODR0akRqUmpNWUZZeHg4amxjK1JPT2xVUHZ4c0xaSmpSMD0iLCJtYWMiOiIxZmQ5ZjdkNjZlZTI1N2Q5NWZhZDBiNTU1Mjk1ZmIxMzlmZmRhMWUzM2E4OWZlZWVmOWQyMDBjNzcxYTAxMWNiIn0_", "name": "<PERSON><PERSON><PERSON> Tankari<PERSON>", "title": "Partner Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6Im5oY0FnZUt2TFZLMHBuRjNmNjg4Qmc9PSIsInZhbHVlIjoidUxQYithZWZLODR0akRqUmpNWUZZeHg4amxjK1JPT2xVUHZ4c0xaSmpSMD0iLCJtYWMiOiIxZmQ5ZjdkNjZlZTI1N2Q5NWZhZDBiNTU1Mjk1ZmIxMzlmZmRhMWUzM2E4OWZlZWVmOWQyMDBjNzcxYTAxMWNiIn0_"}, {"id": "eyJpdiI6ImcrenlkblF2bVEyTjlSZ1JEUFp5T1E9PSIsInZhbHVlIjoiR2ZqVm5jYlVXUEx2UE04SzdBcndVTllOcFVXa29Xbk5xVGY5SmhralVldHlFdHJMSWhZWUdPTXNIUVZQWWdMS3dxbGZ5YmJSY2xwSHdTbVNjTnEzVGc9PSIsIm1hYyI6IjNmYzQ0MzhjNDRjMWVkNjk2MmE4ZDMxZmMyYTk4MTdjY2I1YjMwNWM4NDU4ZDBkOWMyNzdlMWE1NjJiNmE5NDMifQ__", "name": "<PERSON><PERSON>", "title": "Partner Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6ImcrenlkblF2bVEyTjlSZ1JEUFp5T1E9PSIsInZhbHVlIjoiR2ZqVm5jYlVXUEx2UE04SzdBcndVTllOcFVXa29Xbk5xVGY5SmhralVldHlFdHJMSWhZWUdPTXNIUVZQWWdMS3dxbGZ5YmJSY2xwSHdTbVNjTnEzVGc9PSIsIm1hYyI6IjNmYzQ0MzhjNDRjMWVkNjk2MmE4ZDMxZmMyYTk4MTdjY2I1YjMwNWM4NDU4ZDBkOWMyNzdlMWE1NjJiNmE5NDMifQ__"}], "customData": {"SessionLevel": "Level 300 - Advanced", "Topics": ["Generative AI", "Artificial Intelligence"], "SessionType": "Breakout Session", "SessionID": "OET603", "Tracks": "Future of Developers"}, "stats": [], "hiddenTags": "generative ai,artificial intelligence,breakout session,__vipul__tankariya,__surya__ji<PERSON>ra_na<PERSON><PERSON><PERSON>,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET603", "canAddToWatchList": true}, {"id": "1_x3mbjit0", "name": "How <PERSON><PERSON>gy leverages New Relic to scale for its biggest days", "description": "From delivering your morning coffee to greeting you with dinner after work, <PERSON><PERSON><PERSON> provides fast, exceptional service around the clock. To meet demand during peak festivals & sporting events, <PERSON><PERSON><PERSON> employs a secret weapon: intelligent O11y. In this session, learn how <PERSON><PERSON><PERSON> uses New Relic's intelligent observability platform to scale on its biggest days and turn new product ideas into reality.", "type": "7", "updatedAt": 1748352524, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_x3mbjit0/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Building for scale", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": 1750924800, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6Imo0TVptY2drcmRISktWUEdWOWsxZlE9PSIsInZhbHVlIjoiRkN5aUw2bTM3YjFLR2I0OExrenNiZG9hVVVVMHdIZHFwdFpmY1wvclVYd3Jpa0ZMTVBZeklFamtwT09hYW56WHkiLCJtYWMiOiIxYjVmNDAyZGRmOGViYzljN2FkZDI1OTk3ZGExMDA1OWNkYTM1MTIwMDhjN2U4NjEyY2NmMGYxYWU2ZWE0YzE5In0_", "name": "<PERSON><PERSON><PERSON>", "title": "Director of Solutions Consulting, New Relic", "bio": "", "link": "/profile/eyJpdiI6Imo0TVptY2drcmRISktWUEdWOWsxZlE9PSIsInZhbHVlIjoiRkN5aUw2bTM3YjFLR2I0OExrenNiZG9hVVVVMHdIZHFwdFpmY1wvclVYd3Jpa0ZMTVBZeklFamtwT09hYW56WHkiLCJtYWMiOiIxYjVmNDAyZGRmOGViYzljN2FkZDI1OTk3ZGExMDA1OWNkYTM1MTIwMDhjN2U4NjEyY2NmMGYxYWU2ZWE0YzE5In0_"}, {"id": "eyJpdiI6IisxaFdEUFUwVXkxcWp6MElzSFhmMVE9PSIsInZhbHVlIjoic1FkMTRzYXdIRERROHBLWFM5MTdLUFg0amhrMUlcL3JjZ2VVd2Z2MjBoalVtRVRERXpqcVpPWEV6Z1wvc1JyeDh3IiwibWFjIjoiOWY1OWNjNzUzNzkzNmFkYWZhOWJkMzUwNmE2N2U4NGQzNDRiNDVjOTBjNjdjYTdhYzI0OWQ5OTdlZjA4NGJlMiJ9", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Senior Engineering Manager- <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "bio": "", "link": "/profile/eyJpdiI6IisxaFdEUFUwVXkxcWp6MElzSFhmMVE9PSIsInZhbHVlIjoic1FkMTRzYXdIRERROHBLWFM5MTdLUFg0amhrMUlcL3JjZ2VVd2Z2MjBoalVtRVRERXpqcVpPWEV6Z1wvc1JyeDh3IiwibWFjIjoiOWY1OWNjNzUzNzkzNmFkYWZhOWJkMzUwNmE2N2U4NGQzNDRiNDVjOTBjNjdjYTdhYzI0OWQ5OTdlZjA4NGJlMiJ9"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": "Scale on AWS", "SessionType": "Breakout Session", "SessionID": "OET403", "Tracks": "Building for scale"}, "stats": [], "hiddenTags": "scale on aws,breakout session,__prade<PERSON>__<PERSON><PERSON><PERSON><PERSON>,__muth<PERSON><PERSON>__thanga<PERSON>,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET403", "canAddToWatchList": true}, {"id": "1_v3du00ye", "name": "Govern your data and amplify your insights with effective Data Governance", "description": "Join this session for a dive deep into data governance with AWS analytics, exploring the capabilities of Amazon Data & AI Governance.  Discover how to establish robust data governance practices through comprehensive data discovery, cataloging, and lineage tracking. Gain insights into implementing fine-grained access control mechanisms, enabling secure and controlled data access within data mesh reference architectures. Learn about key strategies and best practices for ensuring data integrity, compliance, and effective data management across your organization. Unlock the full potential of your data assets while maintaining governance standards, empowering data-driven decision-making and fostering a culture of trust and transparency.", "type": "7", "updatedAt": 1748352477, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_v3du00ye/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,AWS for data", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": 1750924800, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IjlubjhNb2VzcER1OWtpWHJDVUYxSnc9PSIsInZhbHVlIjoiUGczazVGQ1NrM2RiTk45S21icFRJK0tNT2RrTmliOU1hYnQ2bVUwRnU3V0IxVnVZbXVYcFwvSTl0K1wvM1c4THFoIiwibWFjIjoiZGIwNTk2MjJjZjgwMmM4OGRhZjc4YzNlNzY3YmMxYmE3ODhjYTExYmU3MmZjNmIyYWMyNzc5ZTZlNjkxYzNmYSJ9", "name": "<PERSON>", "title": "Analytics Specialist, AWS India", "bio": "", "link": "/profile/eyJpdiI6IjlubjhNb2VzcER1OWtpWHJDVUYxSnc9PSIsInZhbHVlIjoiUGczazVGQ1NrM2RiTk45S21icFRJK0tNT2RrTmliOU1hYnQ2bVUwRnU3V0IxVnVZbXVYcFwvSTl0K1wvM1c4THFoIiwibWFjIjoiZGIwNTk2MjJjZjgwMmM4OGRhZjc4YzNlNzY3YmMxYmE3ODhjYTExYmU3MmZjNmIyYWMyNzc5ZTZlNjkxYzNmYSJ9"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Big Data", "Databases", "Analytics", "Storage"], "SessionType": "Breakout Session", "SessionID": "OET203", "Tracks": "AWS for data"}, "stats": [], "hiddenTags": "big data,databases,analytics,storage,breakout session,__annie_mattoo_mattoo,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET203", "canAddToWatchList": true}, {"id": "1_o2vtyszr", "name": "Future Forward: Cybersecurity and AI", "description": "Platformization and AI are reshaping cybersecurity. We'll outline the essential steps organizations must take to transition from a reactive, manual, and fragmented security approach to a fully integrated, automated, and proactive strategy. Join us to explore how our security platforms foster simplicity and trust, empowering security teams to drive innovation within their organizations.", "type": "7", "updatedAt": 1748352548, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_o2vtyszr/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Security compliance & resilience", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": 1750924800, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IkU0MnFiZFVSaWZ6OU5LYno0dGhrYUE9PSIsInZhbHVlIjoiOWJqOFhFblRrZ3FMTm5DY1NrUDBQSUNuT0FUNndLY3NNSHlNcFcwY3ZKOD0iLCJtYWMiOiIzMzFiYWEwY2E5N2M0YWQ3ODVkYTI5YTZmNDE2YmRhZWRlMGY2NmQ2OWViYjZjZDg3MGNkYzI3ZjE4NDdhZmJjIn0_", "name": "<PERSON><PERSON>", "title": "Principal Technical Marketing Engineer, Network Security, Palo Alto Network", "bio": "", "link": "/profile/eyJpdiI6IkU0MnFiZFVSaWZ6OU5LYno0dGhrYUE9PSIsInZhbHVlIjoiOWJqOFhFblRrZ3FMTm5DY1NrUDBQSUNuT0FUNndLY3NNSHlNcFcwY3ZKOD0iLCJtYWMiOiIzMzFiYWEwY2E5N2M0YWQ3ODVkYTI5YTZmNDE2YmRhZWRlMGY2NmQ2OWViYjZjZDg3MGNkYzI3ZjE4NDdhZmJjIn0_"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": "Security and Resilience", "SessionType": "Breakout Session", "SessionID": "OET503", "Tracks": "Security compliance & resilience"}, "stats": [], "hiddenTags": "security and resilience,breakout session,__nidhi__pandey,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET503", "canAddToWatchList": true}, {"id": "1_x1hbyk7j", "name": "Observability for serverless workloads", "description": "Serverless computing simplifies infrastructure management—but when issues arise, understanding failures in complex, event-driven systems becomes critical. This session explores what it truly means to be serverless in 2025, and how to implement effective observability strategies on AWS to gain visibility, troubleshoot efficiently, and build resilient, cloud-native applications.", "type": "7", "updatedAt": 1748352454, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_x1hbyk7j/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Generative AI on AWS", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": 1750924800, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IkhybXJ6YUVlVmpHNFFYZGZVWm5RcFE9PSIsInZhbHVlIjoidnJNdExaS0pWXC9HUlgyRG9BbUJ2R1VLdDY0TlkxM0txQnFLUTB5bnNTRDQ9IiwibWFjIjoiZTBiMWRjYTE3NjY5YjZhMzRhNTczZDdiMGY1NWY3ODUzZWU0MTZmNzlmN2FkNDAyN2VlM2Q5NWYzYjM1OTVhNiJ9", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Senior Sales Engineer, Datadog", "bio": "", "link": "/profile/eyJpdiI6IkhybXJ6YUVlVmpHNFFYZGZVWm5RcFE9PSIsInZhbHVlIjoidnJNdExaS0pWXC9HUlgyRG9BbUJ2R1VLdDY0TlkxM0txQnFLUTB5bnNTRDQ9IiwibWFjIjoiZTBiMWRjYTE3NjY5YjZhMzRhNTczZDdiMGY1NWY3ODUzZWU0MTZmNzlmN2FkNDAyN2VlM2Q5NWYzYjM1OTVhNiJ9"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Artificial Intelligence", "Generative AI", "Machine Learning"], "SessionType": "Breakout Session", "SessionID": "OET103", "Tracks": "Generative AI on AWS"}, "stats": [], "hiddenTags": "artificial intelligence,generative ai,machine learning,breakout session,__saurabh__kothari,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET103", "canAddToWatchList": true}, {"id": "1_qzqjqnj6", "name": "Accelerate .NET porting from Windows to Linux with Amazon Q Developer", "description": "Are you dreading the complexity and costs of migrating legacy .NET applications from Windows to Linux? Generative AI can help. In this session, learn how Amazon Q Developer transformation capabilities now automate .NET modernization tasks like dependency analysis and version upgrades, reducing costs by up to 40%. Explore how to rapidly port .NET applications to Linux, transition away from Windows, and break free of the security issues, rising license costs, and challenges with scaling—all while freeing your teams to focus on high-impact features that deliver business value.", "type": "7", "updatedAt": **********, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_qzqjqnj6/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Migration & modernization", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6Inh6Z2xTVVdnM3NyTjQ2Wk0rTWtqeHc9PSIsInZhbHVlIjoiUXVGYWk5MmZjckdxNE9RU1FkRjk1WWNYY0JjNUtYRnkreURcLzdScW9lSUU9IiwibWFjIjoiM2Y1NzhkNjYzNThjYjBmMTQ0MTVkNjhkYmFmMTE3ZGY2ZGI0ZDhkMjMwYmRmYmIyOWY5MDNmNmJkZWM5MzkyMiJ9", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "GTM Specialist Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6Inh6Z2xTVVdnM3NyTjQ2Wk0rTWtqeHc9PSIsInZhbHVlIjoiUXVGYWk5MmZjckdxNE9RU1FkRjk1WWNYY0JjNUtYRnkreURcLzdScW9lSUU9IiwibWFjIjoiM2Y1NzhkNjYzNThjYjBmMTQ0MTVkNjhkYmFmMTE3ZGY2ZGI0ZDhkMjMwYmRmYmIyOWY5MDNmNmJkZWM5MzkyMiJ9"}, {"id": "eyJpdiI6IktBRFZqXC9KdzFURWtFUzRnZkpiSEp3PT0iLCJ2YWx1ZSI6ImhrVmlKZ08rMzlEdXl0WWRaY1ZvbVAwYkxLWGxPWVBidWhKV1l3MFd1cnNrbDRNdHhGUnZlUDN3Y29ZSmxPNWQiLCJtYWMiOiI3ZDQwOTU0ZjU1OTQzOTVlZjQ0NWVmYTNmZGU5M2VjNGIxM2FlMDA5ZTFlNmZiMDZjOGRjNjY3MThhNTAwMGRhIn0_", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Senior GTM Specialist Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6IktBRFZqXC9KdzFURWtFUzRnZkpiSEp3PT0iLCJ2YWx1ZSI6ImhrVmlKZ08rMzlEdXl0WWRaY1ZvbVAwYkxLWGxPWVBidWhKV1l3MFd1cnNrbDRNdHhGUnZlUDN3Y29ZSmxPNWQiLCJtYWMiOiI3ZDQwOTU0ZjU1OTQzOTVlZjQ0NWVmYTNmZGU5M2VjNGIxM2FlMDA5ZTFlNmZiMDZjOGRjNjY3MThhNTAwMGRhIn0_"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Application Modernization", "Building Modern Applications", "Migration and Modernization"], "SessionType": "Breakout Session", "SessionID": "OET304", "Tracks": "Migration & modernization"}, "stats": [], "hiddenTags": "application modernization,building modern applications,migration and modernization,breakout session,__ab<PERSON><PERSON><PERSON>_nanda,__vishwanath__uppala,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET304", "canAddToWatchList": true}, {"id": "1_cn5ev3vk", "name": "Supercharge your productivity across the SDLC with generative AI", "description": "Amazon Q Developer’s powerful agents act as a force multiplier, providing developers with the capabilities of an entire engineering team at their fingertips. Amazon Q Developer’s agents are capable of tackling complex, multistep tasks that go beyond code suggestions, like building features, refactoring code, or generating documentation. They can autonomously plan, code, test, iterate, and implement approved solutions, saving hours daily. Join this chalk talk to learn how new Amazon Q Developer agent capabilities empower developers to focus on more creative work.", "type": "7", "updatedAt": 1748352458, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_cn5ev3vk/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Generative AI on AWS", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IjZJVENka3NaR1Y2S3ptSklsWE9qVlE9PSIsInZhbHVlIjoiTU5rdFZjOGU2Tmt0OGptdlJDRmJua3ZIbHZjM2E2VjltWVFmZE5aZ3gxcUdpdWhtWTZLT0FFMVFQMW5ma3B2eCIsIm1hYyI6IjZlMmMyY2YxODY0YzdkMTI2MGRjYmQyN2U3MDAzYTZmYmFkYzY4ZWRjOTU5NTM3ZTMwNWJkNGM4MDgxMmFiYzYifQ__", "name": "<PERSON><PERSON><PERSON>", "title": "AI & ML Specialist Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6IjZJVENka3NaR1Y2S3ptSklsWE9qVlE9PSIsInZhbHVlIjoiTU5rdFZjOGU2Tmt0OGptdlJDRmJua3ZIbHZjM2E2VjltWVFmZE5aZ3gxcUdpdWhtWTZLT0FFMVFQMW5ma3B2eCIsIm1hYyI6IjZlMmMyY2YxODY0YzdkMTI2MGRjYmQyN2U3MDAzYTZmYmFkYzY4ZWRjOTU5NTM3ZTMwNWJkNGM4MDgxMmFiYzYifQ__"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Artificial Intelligence", "Generative AI", "Machine Learning"], "SessionType": "Breakout Session", "SessionID": "OET104", "Tracks": "Generative AI on AWS"}, "stats": [], "hiddenTags": "artificial intelligence,generative ai,machine learning,breakout session,__sent<PERSON>_j<PERSON><PERSON><PERSON><PERSON>,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET104", "canAddToWatchList": true}, {"id": "1_x3n8ojz6", "name": "Graph-Powered Authorization: Relationship based access (ReBAC) control for access management", "description": "This session provides architects and developers with actionable insights for implementing highly available ReBAC-based authorization system that can scale across enterprise applications and microservices architectures with single digit millisecond latency\n\n In enterprise environments, data lakes store information from various systems of record. In order to build ReBAC system, Extract relationships from specific system data stored in these data lakes. Reference architecture employs one or combination of the following micro-batch solution such as AWS Glue, Amazon Elastic Container Service(ECS) or Amazon Bedrock hosted Foundation Models. This workflow can be triggered using Amazon EventBridge based on data changes within the data lake and orchestrated using AWS Step Functions for execution. Workflow would create or update relationships in Amazon Neptune Database. The relationships are subsequently replicated to ReBAC implementation systems (SpiceDB, AuthZed, Okta etc) using Amazon Neptune Database Streams. \n\n ReBAC implementers provide APIs to define, update, audit and retrieve relationships between users, resources, and permissions. They allow real-time authorization checks to verify if a user or application has specific access to a resource, support exploring and visualizing access hierarchies to understand permission structures and debug issues.", "type": "7", "updatedAt": 1748352528, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_x3n8ojz6/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Building for scale", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IjU2Y0xRZkx3RkZ2cHo1ZllnZzM1T1E9PSIsInZhbHVlIjoiK2lSVDZHTVA1Y1hKRTdjNHUwekhSZGo4TWUwY3UwR0NiWFwvVFRRWkM0TWRLd3kxdGtOSWZXM2ljU3g2WkE2YXM0aVhBd3pTOFFkSFFuVHh1QjNQdjFBPT0iLCJtYWMiOiJiY2U0YzYwNDA0ZTkwODllYzU2Y2E2YzFjZTAzZWU5YTcxYmY0YTAzYmFhY2QzNjQ0ZTgyNDU2N2UzNmNmMGVkIn0_", "name": "Sriharsha <PERSON>rama<PERSON>", "title": "Senior Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6IjU2Y0xRZkx3RkZ2cHo1ZllnZzM1T1E9PSIsInZhbHVlIjoiK2lSVDZHTVA1Y1hKRTdjNHUwekhSZGo4TWUwY3UwR0NiWFwvVFRRWkM0TWRLd3kxdGtOSWZXM2ljU3g2WkE2YXM0aVhBd3pTOFFkSFFuVHh1QjNQdjFBPT0iLCJtYWMiOiJiY2U0YzYwNDA0ZTkwODllYzU2Y2E2YzFjZTAzZWU5YTcxYmY0YTAzYmFhY2QzNjQ0ZTgyNDU2N2UzNmNmMGVkIn0_"}, {"id": "eyJpdiI6InZBMmowQVp3SDRuelFpZWVId21DZFE9PSIsInZhbHVlIjoiXC9Ddks3NVhyR1I1TkVPNytkUHZVRjUyVGgycWNQbGt1ZUpGTlRoelYrRGdhb1Rab0l5bFlQa3JcL2dcL2VETHkxRyIsIm1hYyI6ImNiMjFkOThhZTM5YTY0ZWE5Y2UwMTU3MWMxMzlhNDhmMDQwYTUxZTE3ZWUxYWJmMzNmNmNjYWI1Zjk2MWQ5Y2IifQ__", "name": "<PERSON><PERSON><PERSON>", "title": "Senior Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6InZBMmowQVp3SDRuelFpZWVId21DZFE9PSIsInZhbHVlIjoiXC9Ddks3NVhyR1I1TkVPNytkUHZVRjUyVGgycWNQbGt1ZUpGTlRoelYrRGdhb1Rab0l5bFlQa3JcL2dcL2VETHkxRyIsIm1hYyI6ImNiMjFkOThhZTM5YTY0ZWE5Y2UwMTU3MWMxMzlhNDhmMDQwYTUxZTE3ZWUxYWJmMzNmNmNjYWI1Zjk2MWQ5Y2IifQ__"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": "Scale on AWS", "SessionType": "Breakout Session", "SessionID": "OET404", "Tracks": "Building for scale"}, "stats": [], "hiddenTags": "scale on aws,breakout session,__s<PERSON><PERSON><PERSON>_subramanya_beg<PERSON>i,__yogish_kutkun<PERSON>_pai,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET404", "canAddToWatchList": true}, {"id": "1_gzvoji60", "name": "Supercharge your caching workloads with Valkey on Amazon in-memory databases", "description": "Caching is at the heart of any modern application. Valkey is an open source, in-memory, high performance, key-value datastore and a drop-in replacement for Redis OSS. It can be used for a variety of workloads such as caching, session stores, and message queues, and can act as a primary database.Join this session to learn how Amazon ElastiCache powered by Valkey  speeds up database and application performance, scaling to millions of operations per second with microsecond response time while offering enhanced security and reliability.", "type": "7", "updatedAt": 1748352481, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_gzvoji60/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 300 - Advanced,AWS for data", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6Ik1LQXVLZ3ZWVmVuanN6dHJXUndBdnc9PSIsInZhbHVlIjoiK3JJN2plRGQxMzU4dUt4Q2FqOGpmcmdXSENIZnRKclNDaFBjbUNGWnltOVZuUCs5clRTWEw3VTJ3ekhDRjRKRiIsIm1hYyI6ImYzZjY3ZDZmMzA5ZTNiMGIwMDY3ZTE4NjRlMTRjNGIwNThjNGI1MzA5ODhkYzhhYWE0NmEwNjViNTUwOWMzOWQifQ__", "name": "<PERSON><PERSON><PERSON>", "title": "Senior Worldwide Specialist Solutions Architect, ElastiCache & MemoryDB, AWS", "bio": "", "link": "/profile/eyJpdiI6Ik1LQXVLZ3ZWVmVuanN6dHJXUndBdnc9PSIsInZhbHVlIjoiK3JJN2plRGQxMzU4dUt4Q2FqOGpmcmdXSENIZnRKclNDaFBjbUNGWnltOVZuUCs5clRTWEw3VTJ3ekhDRjRKRiIsIm1hYyI6ImYzZjY3ZDZmMzA5ZTNiMGIwMDY3ZTE4NjRlMTRjNGIwNThjNGI1MzA5ODhkYzhhYWE0NmEwNjViNTUwOWMzOWQifQ__"}], "customData": {"SessionLevel": "Level 300 - Advanced", "Topics": ["Big Data", "Databases", "Analytics", "Storage"], "SessionType": "Breakout Session", "SessionID": "OET204", "Tracks": "AWS for data"}, "stats": [], "hiddenTags": "big data,databases,analytics,storage,breakout session,__shirish_k<PERSON><PERSON><PERSON>_,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET204", "canAddToWatchList": true}, {"id": "1_lb93xy5h", "name": "Create a resilience model using the resilience analysis framework", "description": "By creating a resilience model, you can anticipate what can disrupt your systems, understand what controls are in place to detect and prevent disruption, and then develop an improvement plan to address any gaps. The resilience analysis framework (RAF) helps you to build resilient systems by anticipating failure modes, determining their impact on your systems, and identifying the mitigations that you can apply in case these failures happen. Join this chalk talk to help create a resilience model using RAF.", "type": "7", "updatedAt": 1748352552, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_lb93xy5h/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 300 - Advanced,Security compliance & resilience", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IlJrclwvUnYrNEVNZFFFSFVkTDcxMW5RPT0iLCJ2YWx1ZSI6IjNqbHRicGxWS3RZcVFoV1FESE5DcU9IUE1BZzhNYnhEamJja0dyd2FGc0k9IiwibWFjIjoiYzkwMzFiOGZhNjZmMDdjYjIwMmRiODg5YzE5Mzk3YjQyMGU4NzVlMmQ2OWRiMmFiZGNkODQ3MGIyYjA1MWRiOSJ9", "name": "<PERSON><PERSON><PERSON>", "title": "Senior Technical Customer Solutions Manager, AWS India", "bio": "", "link": "/profile/eyJpdiI6IlJrclwvUnYrNEVNZFFFSFVkTDcxMW5RPT0iLCJ2YWx1ZSI6IjNqbHRicGxWS3RZcVFoV1FESE5DcU9IUE1BZzhNYnhEamJja0dyd2FGc0k9IiwibWFjIjoiYzkwMzFiOGZhNjZmMDdjYjIwMmRiODg5YzE5Mzk3YjQyMGU4NzVlMmQ2OWRiMmFiZGNkODQ3MGIyYjA1MWRiOSJ9"}, {"id": "eyJpdiI6Ijd2c0FQS1d6YzhHZllpSitPbHFwUFE9PSIsInZhbHVlIjoiWW1HdGkzeFVkWnJiaCs1OFArNGFkXC9uNEhUOUZWYXZ5cUN2dmxqU3ZZWlE9IiwibWFjIjoiNzFmODAxNGQ2OTA4ZWY2ZDdiMDBlNzNlMzIxZjVhMDU5YjI4NmRlZDFhYzk5MjE0NWIxMDYxMzk4OTE2YWIzYyJ9", "name": "<PERSON><PERSON><PERSON>", "title": "Technical Account Manager, AWS India", "bio": "", "link": "/profile/eyJpdiI6Ijd2c0FQS1d6YzhHZllpSitPbHFwUFE9PSIsInZhbHVlIjoiWW1HdGkzeFVkWnJiaCs1OFArNGFkXC9uNEhUOUZWYXZ5cUN2dmxqU3ZZWlE9IiwibWFjIjoiNzFmODAxNGQ2OTA4ZWY2ZDdiMDBlNzNlMzIxZjVhMDU5YjI4NmRlZDFhYzk5MjE0NWIxMDYxMzk4OTE2YWIzYyJ9"}], "customData": {"SessionLevel": "Level 300 - Advanced", "Topics": "Security and Resilience", "SessionType": "Breakout Session", "SessionID": "OET504", "Tracks": "Security compliance & resilience"}, "stats": [], "hiddenTags": "security and resilience,breakout session,__an<PERSON><PERSON>_b<PERSON>i,__an<PERSON>ka__gupta,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET504", "canAddToWatchList": true}, {"id": "1_xcfomwwc", "name": "What’s new with Amazon Q Developer agents", "description": "Amazon Q Developer agent capabilities can perform a range of tasks with minimal input—from implementing features and documenting and refactoring code to performing software upgrades. In this session, learn about the newly launched capabilities of the Amazon Q Developer agents. With a deep understanding of your code base, the agents can now help you generate in-depth documentation from source code, iteratively create unit tests across your code base, automatically perform code reviews, and assess the deployment risk of your code updates. See how these new capabilities can help you significantly speed up your entire software development lifecycle and ship higher-quality code to your customers.", "type": "7", "updatedAt": 1748352574, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_xcfomwwc/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Future of Developers", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IklJVGhtMFE4cGRIVnZNd01zM1o0a0E9PSIsInZhbHVlIjoidHdjRVphcTBwRzFoYmhDU0NPakFITjVCYTN4eUpaRFV2V0N6UVluR0pmTkJnejdVVms3aWZaek1GVmRtVW9kMyIsIm1hYyI6ImVhZDI5YjUyZDNiNDFmMGQ2Yjg2MDc4MmUzNjIyYWRkZmM5ZTEyZDE1MWJhNjI5YzlmYjMxOTkyMGZlZTNhM2YifQ__", "name": "<PERSON>", "title": "Technical Account Manager, Enteprise Support, AWS India", "bio": "", "link": "/profile/eyJpdiI6IklJVGhtMFE4cGRIVnZNd01zM1o0a0E9PSIsInZhbHVlIjoidHdjRVphcTBwRzFoYmhDU0NPakFITjVCYTN4eUpaRFV2V0N6UVluR0pmTkJnejdVVms3aWZaek1GVmRtVW9kMyIsIm1hYyI6ImVhZDI5YjUyZDNiNDFmMGQ2Yjg2MDc4MmUzNjIyYWRkZmM5ZTEyZDE1MWJhNjI5YzlmYjMxOTkyMGZlZTNhM2YifQ__"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Generative AI", "Artificial Intelligence"], "SessionType": "Breakout Session", "SessionID": "OET604", "Tracks": "Future of Developers"}, "stats": [], "hiddenTags": "generative ai,artificial intelligence,breakout session,__raja_kumar__thatte,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET604", "canAddToWatchList": true}, {"id": "1_vrgru94l", "name": "Drive business transformation with Amazon Q: Strategy to deployment", "description": "Join us for an engaging session on how Amazon Q is accelerating business transformation by empowering developers and managers to achieve more in less time. From strategic planning to hands-on deployment, discover how Amazon Q’s generative AI capabilities are streamlining development workflows, automating repetitive tasks, and enhancing decision-making. Learn how organizations are leveraging Amazon Q to boost productivity, reduce time-to-market, and foster innovation—enabling teams to focus on high-impact work that drives real business outcomes. Whether you’re a builder or a leader, this session will equip you with practical insights to harness the power of Amazon Q effective", "type": "7", "updatedAt": 1748352578, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_vrgru94l/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Future of Developers", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IlZhUitzeUUzTDJKQ2ZSb3FUREMzcmc9PSIsInZhbHVlIjoieWFtRmJmMjN0bFwvaXg2ZVlvWGd1dXM1UEJHOGxJS2ZMVmZzTWcweGFmNE09IiwibWFjIjoiNTM4MGNjMTBkMjcxNmM2MWExMzNjMTFkYjM3ZDk1ZWFlZTAwNmZmY2Y2NmMxMzI5M2NmNDc0ZTgwYWIzZTEwMyJ9", "name": "<PERSON><PERSON><PERSON>", "title": "Technical Account Manager, Enterprise Support, AWS India", "bio": "", "link": "/profile/eyJpdiI6IlZhUitzeUUzTDJKQ2ZSb3FUREMzcmc9PSIsInZhbHVlIjoieWFtRmJmMjN0bFwvaXg2ZVlvWGd1dXM1UEJHOGxJS2ZMVmZzTWcweGFmNE09IiwibWFjIjoiNTM4MGNjMTBkMjcxNmM2MWExMzNjMTFkYjM3ZDk1ZWFlZTAwNmZmY2Y2NmMxMzI5M2NmNDc0ZTgwYWIzZTEwMyJ9"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Generative AI", "Artificial Intelligence"], "SessionType": "Breakout Session", "SessionID": "OET605", "Tracks": "Future of Developers"}, "stats": [], "hiddenTags": "generative ai,artificial intelligence,breakout session,__shubham__sharan,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET605", "canAddToWatchList": true}, {"id": "1_npa9wqyf", "name": "Deep dive into Amazon DynamoDB zero-ETL integrations", "description": "Amazon DynamoDB is a serverless, NoSQL, fully managed database with single-digit millisecond performance at any scale. DynamoDB lends itself to easy integration with several other AWS services. In this session, dive deep into zero-ETL integrations between Amazon DynamoDB and Amazon SageMaker Lakehouse, Amazon OpenSearch Service, and Amazon Redshift. Learn from AWS experts about how these integrations can reduce operational burden and cost, allowing you to focus on creating value from data instead of preparing data for analysis.", "type": "7", "updatedAt": 1748520637, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_npa9wqyf/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 300 - Advanced,Building for scale", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IlRHczJ1QXlYTHJqRUF1WXBJdWt0WHc9PSIsInZhbHVlIjoiUWN4TkNsd0dPR2ZITHp3VjZUWXZOb3N1OTBiZEhtV0thU0Irc3dWdzlYcz0iLCJtYWMiOiIwZDE1MjRkYWQ5ODZhMjE2ZGY1NGQ0MjcxZWMwMzhlMjQxNzg0YTcwNzEyZWJiNjg1ZTMzNTc3YWYzZjc0NmE0In0_", "name": "<PERSON><PERSON>", "title": "Senior Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6IlRHczJ1QXlYTHJqRUF1WXBJdWt0WHc9PSIsInZhbHVlIjoiUWN4TkNsd0dPR2ZITHp3VjZUWXZOb3N1OTBiZEhtV0thU0Irc3dWdzlYcz0iLCJtYWMiOiIwZDE1MjRkYWQ5ODZhMjE2ZGY1NGQ0MjcxZWMwMzhlMjQxNzg0YTcwNzEyZWJiNjg1ZTMzNTc3YWYzZjc0NmE0In0_"}], "customData": {"SessionLevel": "Level 300 - Advanced", "Topics": ["Scale on AWS", "Containers"], "SessionType": "Breakout Session", "SessionID": "OET405", "Tracks": "Building for scale"}, "stats": [], "hiddenTags": "scale on aws,containers,breakout session,__kunal_kumar,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET405", "canAddToWatchList": true}, {"id": "1_dgn7wgwb", "name": "Securing applications from bots, fraud, and generative AI powered workloads with AWS WAF", "description": "Securing web applications in today’s ever-evolving threat landscape is crucial. As threats evolve, so must security controls and countermeasures. In this session, dive into how AWS WAF seamlessly integrates with other AWS services and help you prevent bad bots and frauds, making it possible for you to construct a resilient, multi-layered defense strategy. Learn about uncommon use cases and how to address even the most unconventional threats.", "type": "7", "updatedAt": 1748612363, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_dgn7wgwb/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 300 - Advanced,Security compliance & resilience", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6ImhpS2JxUnBwTlJPWmtVblZ6aGc0TUE9PSIsInZhbHVlIjoicUhndlZKWVNYSnQ1WWhacmw5WXVzTGR4blJjQjJTU0owZ2h5a1wvUjRNUyt4SGRTeVp4cUNOV0l5NHk2bVBycHJTN3pwQVwvU2NyZWNVeUVVeDI2Um1Qdz09IiwibWFjIjoiMzQxOTEzOTdhMzZhNTY5OWFiODFjM2RlNzFlZDgwMmZiYzljOTAyZjdhMmVjOWE2Yjg2YjZiNGZiNmE3NzA1YSJ9", "name": "<PERSON><PERSON><PERSON>", "title": "Senior Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6ImhpS2JxUnBwTlJPWmtVblZ6aGc0TUE9PSIsInZhbHVlIjoicUhndlZKWVNYSnQ1WWhacmw5WXVzTGR4blJjQjJTU0owZ2h5a1wvUjRNUyt4SGRTeVp4cUNOV0l5NHk2bVBycHJTN3pwQVwvU2NyZWNVeUVVeDI2Um1Qdz09IiwibWFjIjoiMzQxOTEzOTdhMzZhNTY5OWFiODFjM2RlNzFlZDgwMmZiYzljOTAyZjdhMmVjOWE2Yjg2YjZiNGZiNmE3NzA1YSJ9"}, {"id": "eyJpdiI6IjNFWWVWbWsweklwYjBVOUVTWFhEZHc9PSIsInZhbHVlIjoiQkk0ZGVzTFpkRm9lNWd3QXFhVVlDeThrUmt0bzhPR2lXK0FYdVFGNG9XTT0iLCJtYWMiOiJiOGI1NGNmODM3OTcyZWE1Njg2NWMxZDQwMTk1N2YzODcyMzI2NDA0ZWY3ZmQ4ZTk4YWY1NDJjM2E5NjM4NzYzIn0_", "name": "<PERSON><PERSON><PERSON>", "title": "Senior Cloud Support Engineer, AWS India", "bio": "", "link": "/profile/eyJpdiI6IjNFWWVWbWsweklwYjBVOUVTWFhEZHc9PSIsInZhbHVlIjoiQkk0ZGVzTFpkRm9lNWd3QXFhVVlDeThrUmt0bzhPR2lXK0FYdVFGNG9XTT0iLCJtYWMiOiJiOGI1NGNmODM3OTcyZWE1Njg2NWMxZDQwMTk1N2YzODcyMzI2NDA0ZWY3ZmQ4ZTk4YWY1NDJjM2E5NjM4NzYzIn0_"}], "customData": {"SessionLevel": "Level 300 - Advanced", "Topics": "Security and Resilience", "SessionType": "Breakout Session", "SessionID": "OET505", "Tracks": "Security compliance & resilience"}, "stats": [], "hiddenTags": "security and resilience,breakout session,__praveen_hosur_narayana_gupta,__de<PERSON><PERSON>_agra<PERSON>,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET505", "canAddToWatchList": true}, {"id": "1_oa16e3or", "name": "Making dollars and sense out of FinOps", "description": "In today’s cloud-centric landscape, understanding and controlling cloud costs is crucial. It’s important for leaders to understand how financial processes and strategies translate into clear and actionable insights in both the short and long term. FinOps, the practice of managing cloud costs, offers organizations a powerful methodology to optimize financial management and drive business value. In this session, representatives from the Enterprise Strategy team at AWS discuss key FinOps concepts and methodologies, as well as real-world examples of how organizations achieve cost savings while delivering business value for their organization. Receive practical guidance intended to empower you to implement FinOps.", "type": "7", "updatedAt": 1748352508, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_oa16e3or/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Migration & modernization", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IkpNSnFyVWk5RVZLT3FIeGF4R1k3SEE9PSIsInZhbHVlIjoic1JiRk1LOVUwMjRBbms4dTV0d1czQlhVcVB2UTFjM1FxcGpKS2xNYWtSWWFMNHNIRXN3U09FWTlYT0IzRlM5diIsIm1hYyI6IjQzZGE0ODhhYTE5ZTMwNTI5OGM4NDMzYTNmNDUzYjRjNTZmN2Y5MTgyYjQ2Y2U0NmI4ODE0YjIyMzIyZTFmN2UifQ__", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Business Developer, Migrations, AWS India", "bio": "", "link": "/profile/eyJpdiI6IkpNSnFyVWk5RVZLT3FIeGF4R1k3SEE9PSIsInZhbHVlIjoic1JiRk1LOVUwMjRBbms4dTV0d1czQlhVcVB2UTFjM1FxcGpKS2xNYWtSWWFMNHNIRXN3U09FWTlYT0IzRlM5diIsIm1hYyI6IjQzZGE0ODhhYTE5ZTMwNTI5OGM4NDMzYTNmNDUzYjRjNTZmN2Y5MTgyYjQ2Y2U0NmI4ODE0YjIyMzIyZTFmN2UifQ__"}, {"id": "eyJpdiI6IlBLNnJNQzhjcVBMQjROaXg0KzZPVlE9PSIsInZhbHVlIjoiUzBZYTVRUUtpbVluemZZSlZJMnpmWnJ4eVFCNGpwM3B4TzFBbUk3RXZ5VT0iLCJtYWMiOiI4MjhmYWFiZmE1ZTI5MGM2NzQzMDMyOWViNjNjMzM4NDhhN2FiMzI2ZTJhMmU0YTU0NGEyYjExMGUyZjdhN2QwIn0_", "name": "<PERSON><PERSON>", "title": "Manager, Technical Customer Solutions, AWS India", "bio": "", "link": "/profile/eyJpdiI6IlBLNnJNQzhjcVBMQjROaXg0KzZPVlE9PSIsInZhbHVlIjoiUzBZYTVRUUtpbVluemZZSlZJMnpmWnJ4eVFCNGpwM3B4TzFBbUk3RXZ5VT0iLCJtYWMiOiI4MjhmYWFiZmE1ZTI5MGM2NzQzMDMyOWViNjNjMzM4NDhhN2FiMzI2ZTJhMmU0YTU0NGEyYjExMGUyZjdhN2QwIn0_"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Application Modernization", "Building Modern Applications", "Migration and Modernization"], "SessionType": "Breakout Session", "SessionID": "OET305", "Tracks": "Migration & modernization"}, "stats": [], "hiddenTags": "application modernization,building modern applications,migration and modernization,breakout session,__pratip<PERSON>_anubhav,__a<PERSON>_sangal,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET305", "canAddToWatchList": true}, {"id": "1_gc2zekmt", "name": "Revolutionize Data Warehousing with Amazon Redshift", "description": "Join this session to learn about the newest innovations in data warehousing and SQL analytics with AWS analytics services. Amazon Redshift is the AI-powered, cloud-based data warehousing solution used by tens of thousands of AWS customers to modernize data analytics workloads and generate business insights with the best price performance. Learn more about the latest capabilities launched for Amazon Redshift to further drive quick decision-making with lower costs for your organization.", "type": "7", "updatedAt": 1748352485, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_gc2zekmt/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 300 - Advanced,AWS for data", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IndkV1dXUTIwV3JIWlAxVW40RGExR0E9PSIsInZhbHVlIjoiY2Z4ZnpHRzlyT0NxXC9iREhYb1B1V2VnZVRkZjIzNTRyRHl2djkyYWdWK3hwSDM1eVE4RTFvbzJKa2RTNURMYzMiLCJtYWMiOiJjYTc0OWQwYWE3Y2U4MDY3YzQwM2RkNTk3MzEyY2I0ZGRkMWZiMDBlNzJjNmNiMDY3MGM5NDIyYTYwYTcxNjNkIn0_", "name": "<PERSON><PERSON><PERSON>", "title": "Senior Technical Account Manager, AWS India", "bio": "", "link": "/profile/eyJpdiI6IndkV1dXUTIwV3JIWlAxVW40RGExR0E9PSIsInZhbHVlIjoiY2Z4ZnpHRzlyT0NxXC9iREhYb1B1V2VnZVRkZjIzNTRyRHl2djkyYWdWK3hwSDM1eVE4RTFvbzJKa2RTNURMYzMiLCJtYWMiOiJjYTc0OWQwYWE3Y2U4MDY3YzQwM2RkNTk3MzEyY2I0ZGRkMWZiMDBlNzJjNmNiMDY3MGM5NDIyYTYwYTcxNjNkIn0_"}], "customData": {"SessionLevel": "Level 300 - Advanced", "Topics": ["Big Data", "Databases", "Analytics", "Storage"], "SessionType": "Breakout Session", "SessionID": "OET205", "Tracks": "AWS for data"}, "stats": [], "hiddenTags": "big data,databases,analytics,storage,breakout session,__sornavel_perumal,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET205", "canAddToWatchList": true}, {"id": "1_40ym4mqe", "name": "Accelerate gen AI: Amazon SageMaker HyperPod training plans & recipes", "description": "Amazon SageMaker HyperPod is purpose built for developing foundation models (FM), reducing time to train by up to 40%. To accelerate generative AI innovation even more, SageMaker HyperPod now empowers users to create flexible training plans with multiple blocks of accelerated compute resources, allowing them to train FMs within their budgets and timelines. In addition, users can get started quickly with the ready-to-use SageMaker HyperPod recipes to train and fine-tune popular publicly available FMs in hours instead of weeks with state-of-the-art cost performance.", "type": "7", "updatedAt": 1748352461, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_40ym4mqe/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Generative AI on AWS", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IlwvNVBRNVlKRE1VMWV3YnhBalNmcE5nPT0iLCJ2YWx1ZSI6ImtRVVcrN1FUc1EzTGJYWFFDXC9UQUNJejg5MmJTMDRLQzY3dnZZa3E1TnRjPSIsIm1hYyI6ImNmYjVhYjM0ODdjZDNhZDY5N2EwNmMyZTQ4OGFjODkwNzM0MjU1MjcxMTJkMzVjNjgyMzRhMDI5MWI4ZmNmODQifQ__", "name": "<PERSON><PERSON><PERSON>", "title": "Prototyping Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6IlwvNVBRNVlKRE1VMWV3YnhBalNmcE5nPT0iLCJ2YWx1ZSI6ImtRVVcrN1FUc1EzTGJYWFFDXC9UQUNJejg5MmJTMDRLQzY3dnZZa3E1TnRjPSIsIm1hYyI6ImNmYjVhYjM0ODdjZDNhZDY5N2EwNmMyZTQ4OGFjODkwNzM0MjU1MjcxMTJkMzVjNjgyMzRhMDI5MWI4ZmNmODQifQ__"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Artificial Intelligence", "Generative AI", "Machine Learning"], "SessionType": "Breakout Session", "SessionID": "OET105", "Tracks": "Generative AI on AWS"}, "stats": [], "hiddenTags": "artificial intelligence,generative ai,machine learning,breakout session,__aastha_verma,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET105", "canAddToWatchList": true}, {"id": "1_ae64gzfp", "name": "Accelerate multistep SDLC tasks with Amazon Q Developer Agent", "description": "While previous AI assistants focus on code generation with close human guidance, Amazon Q Developer has a unique capability called Amazon Q Developer Agent that can use reasoning and planning capabilities to perform multi-step tasks beyond code generation with minimal human intervention. Its agent for software development can solve complex tasks that go beyond code suggestions, from building application features to automatic code reviews, unit tests, and documentation generation. In this session, discover how the new agent capabilities help developers go from planning to building new features faster. Also, hear from DTCC on how they use Amazon Q to streamline their development processes.", "type": "7", "updatedAt": 1748520638, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_ae64gzfp/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 100 - Introductory,Building for scale", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": 1750932000, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6InU1RVhKa2ZoYWtMQzNFT2UwRHlIR0E9PSIsInZhbHVlIjoiTm8xWm81SVhTWllTTVNFbElScEsxaFZuZks2WGxUNUptb0xVTWFUWjB5TT0iLCJtYWMiOiIxZGI0MGFiOGJhOTc5YjU3YzU4NjJkYTc1ZGQ5MjM0YzU4MDViZjg2YTE4MzM2MjhhZDFjOTA0MzI2N2RmNjE5In0_", "name": "<PERSON><PERSON>", "title": "Senior Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6InU1RVhKa2ZoYWtMQzNFT2UwRHlIR0E9PSIsInZhbHVlIjoiTm8xWm81SVhTWllTTVNFbElScEsxaFZuZks2WGxUNUptb0xVTWFUWjB5TT0iLCJtYWMiOiIxZGI0MGFiOGJhOTc5YjU3YzU4NjJkYTc1ZGQ5MjM0YzU4MDViZjg2YTE4MzM2MjhhZDFjOTA0MzI2N2RmNjE5In0_"}], "customData": {"SessionLevel": "Level 100 - Introductory", "Topics": "Scale on AWS", "SessionType": "Breakout Session", "SessionID": "OET406", "Tracks": "Building for scale"}, "stats": [], "hiddenTags": "scale on aws,breakout session,__sonika__beniwal,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET406", "canAddToWatchList": true}, {"id": "1_jzzdignf", "name": "Store tabular data at scale with Amazon S3 Tables", "description": "Amazon S3 Tables is purpose-built to store tabular data in Apache Iceberg tables. With Amazon S3 Tables, you can create tables and set up table-level permissions with just a few clicks in the Amazon S3 console. These tables are backed by storage specifically built for tabular data, resulting in higher transactions per second and better query throughput compared to unmanaged tables in storage. Join this session to learn how you can automate table management tasks such as compaction, snapshot management, and more with Amazon S3 to continuously optimize query performance and minimize cost.", "type": "7", "updatedAt": 1748352489, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_jzzdignf/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 300 - Advanced,AWS for data", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": 1750932000, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6Ikw2QlhkbUduWG1UVG5GSXFvTzdTaXc9PSIsInZhbHVlIjoiUGZ3M0hmV090ZVVmNVNMeEVmYVh3UFplMFkwZXh1TFFQSmNcL0ZVU3R0UUhDeUkyblZ2eVpQK05kUlNzK0VkNFciLCJtYWMiOiJlYmUxYzE0NDhjODk3NmU2YmE2OWVlNWY0NmQ1ODY1ZWFiNDAzNWJlMzBkZDI3NDcxOWU3ZGMzOTdiNzQwYzFjIn0_", "name": "<PERSON><PERSON>", "title": "Senior Storage Specialist Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6Ikw2QlhkbUduWG1UVG5GSXFvTzdTaXc9PSIsInZhbHVlIjoiUGZ3M0hmV090ZVVmNVNMeEVmYVh3UFplMFkwZXh1TFFQSmNcL0ZVU3R0UUhDeUkyblZ2eVpQK05kUlNzK0VkNFciLCJtYWMiOiJlYmUxYzE0NDhjODk3NmU2YmE2OWVlNWY0NmQ1ODY1ZWFiNDAzNWJlMzBkZDI3NDcxOWU3ZGMzOTdiNzQwYzFjIn0_"}], "customData": {"SessionLevel": "Level 300 - Advanced", "Topics": ["Big Data", "Databases", "Analytics", "Storage"], "SessionType": "Breakout Session", "SessionID": "OET206", "Tracks": "AWS for data"}, "stats": [], "hiddenTags": "big data,databases,analytics,storage,breakout session,__sandeep_aggarwal,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET206", "canAddToWatchList": true}, {"id": "1_h9tbfm2j", "name": "AWS Trainium2 for breakthrough AI training and inference performance", "description": "Introducing the new Amazon EC2 Trn2 instances featuring AWS Trainium2, the second-generation of the Trainium chip. Attend this session for a first look at how Trn2 instances deliver the highest performance for training and deploying the most demanding generative AI models with hundreds of billions to trillions of parameters. Dive deep into how Trn2 instances have been designed to help you to reduce training times, iterate faster, and deliver real-time AI-powered experiences.", "type": "7", "updatedAt": 1748352465, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_h9tbfm2j/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Generative AI on AWS", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": 1750932000, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IkhYaWRmS2lKV3U0M0I0V0JFTFBWcmc9PSIsInZhbHVlIjoiRVpRRUQ2ckx5OG9tcXAxbXo3cVlvTGdaUnBuUDlKaWNSZUpGYm1HY2JnST0iLCJtYWMiOiJhN2QyZGZiY2IzZTYxNTViZjRkYzI4Y2JmZGQ0M2U2NDQzNjRlMTA3OTA2ZDJkOGYwYmNhYTc2YWQ4OWY3ZmFlIn0_", "name": "<PERSON><PERSON><PERSON>", "title": "Senior Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6IkhYaWRmS2lKV3U0M0I0V0JFTFBWcmc9PSIsInZhbHVlIjoiRVpRRUQ2ckx5OG9tcXAxbXo3cVlvTGdaUnBuUDlKaWNSZUpGYm1HY2JnST0iLCJtYWMiOiJhN2QyZGZiY2IzZTYxNTViZjRkYzI4Y2JmZGQ0M2U2NDQzNjRlMTA3OTA2ZDJkOGYwYmNhYTc2YWQ4OWY3ZmFlIn0_"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Artificial Intelligence", "Generative AI", "Machine Learning"], "SessionType": "Breakout Session", "SessionID": "OET106", "Tracks": "Generative AI on AWS"}, "stats": [], "hiddenTags": "artificial intelligence,generative ai,machine learning,breakout session,__darshit_vohra,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET106", "canAddToWatchList": true}, {"id": "1_24bcz62o", "name": "Scaling multi-tenant SaaS with a cell-based architecture", "description": "This session presents a transformative approach to building resilient multi-tenant SaaS applications through cell-based architecture on AWS. Drawing inspiration from naval engineering's bulkhead design pattern, we'll demonstrate how organizations can overcome critical challenges in blast radius reduction and scalability.\nThe presentation and demo will showcase a solution comprising of three core components: the cell management system, cell router, and cell application plane. We'll explore how these components work together to create isolated, scalable units that contain failures and enable predictable growth. The architecture leverages a serverless-first approach using AWS Lambda, Step Functions, CloudFront Functions, EventBridge, DynamoDB, ECS, Aurora PostgreSQL, and other AWS services to create a robust solution for modern SaaS applications.", "type": "7", "updatedAt": 1748352559, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_24bcz62o/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 300 - Advanced,Security compliance & resilience", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": 1750932000, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6IkpqdTZJSkxMTm9mNzdDQXF6Q2VaekE9PSIsInZhbHVlIjoiNjRncmF5VnpSUGE3c0Vwb05yYThcLzFwWjZGXC84OUV2eVV5Vml0NUM3c29ZPSIsIm1hYyI6IjVjY2M1OTk5OGQ0ZWM2NWRjM2Y3NzczYmJjMTRlZjhiNGFhOTM1ZDM2YTU4NzIzZTM4NDIxMDJmNzA0YjE2OGEifQ__", "name": "<PERSON><PERSON>", "title": "Enterprise Services Lead, AWS India", "bio": "", "link": "/profile/eyJpdiI6IkpqdTZJSkxMTm9mNzdDQXF6Q2VaekE9PSIsInZhbHVlIjoiNjRncmF5VnpSUGE3c0Vwb05yYThcLzFwWjZGXC84OUV2eVV5Vml0NUM3c29ZPSIsIm1hYyI6IjVjY2M1OTk5OGQ0ZWM2NWRjM2Y3NzczYmJjMTRlZjhiNGFhOTM1ZDM2YTU4NzIzZTM4NDIxMDJmNzA0YjE2OGEifQ__"}, {"id": "eyJpdiI6IkNvSDZ2WUMwOHhaZ2FMQjh1MVlQYUE9PSIsInZhbHVlIjoiQjNCQWxBV3V3R1BoUkhQU0FCbWNGVFpEa0k3SHQ2UzU3MGM5Q2xFeVdlUT0iLCJtYWMiOiJiZWE1ZTMzNzg1MDhiMjFiOWZmMmM5ZWFiNGFjYTllNWYwYzI4ZDEyYmU0M2ZjODQwMTQ4ZTg2ZTYzZDI3MmE3In0_", "name": "<PERSON><PERSON>", "title": "Senior Technical Account Manager, AWS India", "bio": "", "link": "/profile/eyJpdiI6IkNvSDZ2WUMwOHhaZ2FMQjh1MVlQYUE9PSIsInZhbHVlIjoiQjNCQWxBV3V3R1BoUkhQU0FCbWNGVFpEa0k3SHQ2UzU3MGM5Q2xFeVdlUT0iLCJtYWMiOiJiZWE1ZTMzNzg1MDhiMjFiOWZmMmM5ZWFiNGFjYTllNWYwYzI4ZDEyYmU0M2ZjODQwMTQ4ZTg2ZTYzZDI3MmE3In0_"}], "customData": {"SessionLevel": "Level 300 - Advanced", "Topics": "Security and Resilience", "SessionType": "Breakout Session", "SessionID": "OET506", "Tracks": "Security compliance & resilience"}, "stats": [], "hiddenTags": "security and resilience,breakout session,__meera_k<PERSON><PERSON><PERSON>ot,__asad_baig_,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET506", "canAddToWatchList": true}, {"id": "1_5yalsd25", "name": "From chaos to code: Tackling the hardest dev problems with AI-Driven Development LifeCycle", "description": "In a world where developers grapple with legacy systems, shifting requirements, brittle architectures, and never-ending backlogs, the real bottleneck isn’t going to be coding, but it’s decision-making, validating and offering oversight to AI. This session dives into the AI-Driven Development LifeCycle (AI-DLC), a reimagined software engineering methodology.  It offers the much sought after perspectives on the future of developmenr with real world examples and best practices on solving the hardest developer challenges by applying AI as a core collaborator in the SDLC.", "type": "7", "updatedAt": 1748352582, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_5yalsd25/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 300 - Advanced,Future of Developers", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": 1750932000, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6InJmR1wvYzZMMFZOczdrRFJCcFNiNkxRPT0iLCJ2YWx1ZSI6Inp3UVI4VEVoeXFKdHdhSStyY1Y2TURtQ1NPSXRTblwveXMwbjdMZXBrTkEwPSIsIm1hYyI6ImU4NWM3Y2ZkNmFjZjY0ODNlMzNjYTc4ZTBlZjU0YWIxMDFhNzg4ZmFhNjIzNGQ5ODEzNzU3OWIwMzE1YzhlN2MifQ__", "name": "<PERSON><PERSON><PERSON>", "title": "Solutions Architects Manager, Developer Transformation, AWS India", "bio": "", "link": "/profile/eyJpdiI6InJmR1wvYzZMMFZOczdrRFJCcFNiNkxRPT0iLCJ2YWx1ZSI6Inp3UVI4VEVoeXFKdHdhSStyY1Y2TURtQ1NPSXRTblwveXMwbjdMZXBrTkEwPSIsIm1hYyI6ImU4NWM3Y2ZkNmFjZjY0ODNlMzNjYTc4ZTBlZjU0YWIxMDFhNzg4ZmFhNjIzNGQ5ODEzNzU3OWIwMzE1YzhlN2MifQ__"}], "customData": {"SessionLevel": "Level 300 - Advanced", "Topics": ["Generative AI", "Artificial Intelligence"], "SessionType": "Breakout Session", "SessionID": "OET606", "Tracks": "Future of Developers"}, "stats": [], "hiddenTags": "generative ai,artificial intelligence,breakout session,__tushar__agar<PERSON>,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET606", "canAddToWatchList": true}, {"id": "1_gl4zh7s0", "name": "Simplifying multi-tenancy with SaaS applications on AWS Fargate", "description": "There is a growing trend to build SaaS solutions on Amazon ECS. Developing multi-tenant SaaS applications requires addressing multiple concerns, including tenant isolation, tenant onboarding, tenant-specific metering, monitoring, and other SaaS aspects. In this session, explore how to manage multi-tenancy aspects when deploying solutions on Amazon ECS with AWS Fargate.", "type": "7", "updatedAt": 1748352512, "thumbnailUrl": "https://cfvod.kaltura.com/p/6219222/sp/*********/thumbnail/entry_id/1_gl4zh7s0/version/100011/width/379/height/213/type/3/quality/100", "tags": "Level 200 - Intermediate,Migration & modernization", "mediaType": 201, "duration": 0, "schedulingData": {"start": {"timestamp": **********, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}, "end": {"timestamp": 1750932000, "timeZoneName": "Asia/Kolkata", "timeZoneOffset": 19800}}, "presenters": [{"id": "eyJpdiI6InlhU3ZjVG1TK0U0QWs5dExUV1ZpN0E9PSIsInZhbHVlIjoiR2szeWtcL29YdFRsa2hicngrVmpEMCtEZWQ5VmdoeUtwSG5jVWJTdVwvRzNrbGJMVnp2bXdnVTE1UEtkYmhBSGhTIiwibWFjIjoiMGU3OTI2MTM1NzU3NDA0N2I5ZWJjNTQ3MTYyOWJiMzE2MTdiYzE2MTM1ZTAyZDljZTY0Y2Q4YzMwZDlkYTc0NCJ9", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6InlhU3ZjVG1TK0U0QWs5dExUV1ZpN0E9PSIsInZhbHVlIjoiR2szeWtcL29YdFRsa2hicngrVmpEMCtEZWQ5VmdoeUtwSG5jVWJTdVwvRzNrbGJMVnp2bXdnVTE1UEtkYmhBSGhTIiwibWFjIjoiMGU3OTI2MTM1NzU3NDA0N2I5ZWJjNTQ3MTYyOWJiMzE2MTdiYzE2MTM1ZTAyZDljZTY0Y2Q4YzMwZDlkYTc0NCJ9"}, {"id": "eyJpdiI6Imt0TkdrOUlORldjUGZUWThzbE1VbWc9PSIsInZhbHVlIjoia011UUlwNDF3dmFTaVNWd21iVEU0MDdQOTlyam93TGFWS3VjdnVlS2pKQT0iLCJtYWMiOiJkYzRjMjVhY2MwOTY2ZjZlYWQwOGJjY2NjZDU1OWEwODljMTRlN2NiMjMwY2IwN2I1NzE0Zjc1YmRmYWEzOTQ1In0_", "name": "<PERSON><PERSON><PERSON>", "title": "Associate Solutions Architect, AWS India", "bio": "", "link": "/profile/eyJpdiI6Imt0TkdrOUlORldjUGZUWThzbE1VbWc9PSIsInZhbHVlIjoia011UUlwNDF3dmFTaVNWd21iVEU0MDdQOTlyam93TGFWS3VjdnVlS2pKQT0iLCJtYWMiOiJkYzRjMjVhY2MwOTY2ZjZlYWQwOGJjY2NjZDU1OWEwODljMTRlN2NiMjMwY2IwN2I1NzE0Zjc1YmRmYWEzOTQ1In0_"}], "customData": {"SessionLevel": "Level 200 - Intermediate", "Topics": ["Application Modernization", "Building Modern Applications", "Migration and Modernization"], "SessionType": "Breakout Session", "SessionID": "OET306", "Tracks": "Migration & modernization"}, "stats": [], "hiddenTags": "application modernization,building modern applications,migration and modernization,breakout session,__di<PERSON><PERSON><PERSON>_shukla,__rohit_raj,__alfie_order1", "callToActionLink": "https://amazonmr.au1.qualtrics.com/jfe/form/SV_032RyZJph3xeCZE?session=OET306", "canAddToWatchList": true}]